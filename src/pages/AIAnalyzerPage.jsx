
import { useState, useEffect, useCallback, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { authAPI, projectAPI } from '../services/api';
import '../styles/AIAnalyzerPage.css';

const AIAnalyzerPage = () => {
  const navigate = useNavigate();
  const dropdownRef = useRef(null);
  const [projects, setProjects] = useState([]);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [language, setLanguage] = useState(() => {
    const savedLang = localStorage.getItem('preferredLanguage');
    return savedLang || 'zh';
  });
  const [activeTab, setActiveTab] = useState('all');
  const [newProject, setNewProject] = useState({
    name: '',
    description: ''
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [currentUser, setCurrentUser] = useState(null);
  const [showLanguageDropdown, setShowLanguageDropdown] = useState(false);
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  // 翻译对象
  const translations = {
    en: {
      title: 'AI Site Analyzer',
      subtitle: 'Intelligent Industrial Site Analysis Platform',
      createProject: 'Create Project',
      projectName: 'Project Name',
      projectDescription: 'Description',
      create: 'Create',
      cancel: 'Cancel',
      noProjects: 'No projects yet',
      noProjectsDesc: 'Create your first project to start analyzing industrial sites with AI',
      createdAt: 'Created',
      openProject: 'Open',
      deleteProject: 'Delete',
      backToHome: 'Back to Home',
      allProjects: 'All Projects',
      favorites: 'Favorites',
      starProject: 'Star',
      unstarProject: 'Unstar',
      loadingProjects: 'Loading...',
      projectLoadError: 'Failed to load projects',
      retryButton: 'Retry',
      loginRequired: 'Welcome to AI Site Analyzer',
      loginToCreateProjects: 'Sign in to create and manage your industrial site analysis projects',
      loginButton: 'Sign In',
      continueAsGuest: 'Continue as Guest',
      statusActive: 'Active',
      statusInProgress: 'In Progress',
      statusCompleted: 'Completed',
      createSuccess: 'Project created successfully',
      deleteSuccess: 'Project deleted successfully',
      deleteConfirm: 'Are you sure you want to delete this project? This action cannot be undone.',
      creating: 'Creating...',
      noFavorites: 'No favorite projects',
      noFavoritesDesc: 'Star important projects for quick access',
      projectCount: 'projects',
      favoriteCount: 'favorites'
    },
    zh: {
      title: 'AI 选址分析器',
      subtitle: '智能工业选址分析平台',
      createProject: '创建项目',
      projectName: '项目名称',
      projectDescription: '项目描述',
      create: '创建',
      cancel: '取消',
      noProjects: '暂无项目',
      noProjectsDesc: '创建您的第一个项目，开始AI智能工业选址分析',
      createdAt: '创建时间',
      openProject: '打开',
      deleteProject: '删除',
      backToHome: '返回首页',
      allProjects: '全部项目',
      favorites: '收藏夹',
      starProject: '收藏',
      unstarProject: '取消收藏',
      loadingProjects: '加载中...',
      projectLoadError: '加载项目失败',
      retryButton: '重试',
      loginRequired: '欢迎使用AI选址分析器',
      loginToCreateProjects: '登录以创建和管理您的工业选址分析项目',
      loginButton: '登录',
      continueAsGuest: '以访客身份继续',
      statusActive: '活跃',
      statusInProgress: '进行中',
      statusCompleted: '已完成',
      createSuccess: '项目创建成功',
      deleteSuccess: '项目删除成功',
      deleteConfirm: '确定要删除这个项目吗？此操作无法撤销。',
      creating: '创建中...',
      noFavorites: '暂无收藏项目',
      noFavoritesDesc: '收藏重要项目以便快速访问',
      projectCount: '个项目',
      favoriteCount: '个收藏'
    }
  };

  const t = translations[language] || translations.zh;

  // 检查认证状态
  const checkAuthenticationStatus = useCallback(() => {
    const isAuth = authAPI.isAuthenticated();
    const user = authAPI.getUser();
    setIsAuthenticated(isAuth);
    setCurrentUser(user);
    return { isAuth, user };
  }, []);

  // 初始化
  useEffect(() => {
    const initializeComponent = async () => {
      console.log('初始化AI分析器页面...');
      
      // 设置页面专用的body类名以激活对应背景
      document.body.classList.add('ai-analyzer-active');
      document.body.classList.remove('ai-project-active', 'main-page-active');
      
      const { isAuth } = checkAuthenticationStatus();

      if (!isAuth) {
        console.log('用户未认证，显示登录提示');
        setIsAuthenticated(false);
        setProjects([]);
        return;
      }

      // 如果已认证，加载项目
      await loadProjects();
    };

    initializeComponent();
    
    // 清理函数：组件卸载时移除类名
    return () => {
      document.body.classList.remove('ai-analyzer-active');
    };
  }, []);

  // 保存语言设置
  useEffect(() => {
    localStorage.setItem('preferredLanguage', language);
  }, [language]);

  // 加载项目列表
  const loadProjects = async () => {
    if (!authAPI.isAuthenticated()) {
      console.log('用户未认证，跳过加载项目');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      console.log('正在加载项目列表...');
      const response = await projectAPI.getProjects();
      console.log('项目API响应:', response);

      if (response.success) {
        const formattedProjects = response.data.projects.map(project => ({
          id: project.id.toString(),
          name: project.project_name,
          description: project.description || '',
          createdAt: project.created_at,
          updatedAt: project.updated_at,
          status: project.status || 'active',
          starred: Boolean(project.isFavorite)
        }));

        setProjects(formattedProjects);
        console.log('项目加载成功:', formattedProjects.length, '个项目');
      } else {
        throw new Error(response.message || 'API返回失败状态');
      }
    } catch (error) {
      console.error('加载项目失败:', error);
      if (error.message.includes('401') || error.message.includes('403')) {
        // 认证失败，清除认证状态
        authAPI.logout();
        setIsAuthenticated(false);
        setCurrentUser(null);
        setError('认证已过期，请重新登录');
      } else {
        setError(t.projectLoadError);
      }
    } finally {
      setIsLoading(false);
    }
  };

  // 处理点击外部关闭下拉菜单
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setShowLanguageDropdown(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // 计算显示的项目
  const displayedProjects = projects.filter(project => 
    activeTab === 'all' || (activeTab === 'favorites' && project.starred)
  );

  // 计算项目统计
  const projectStats = {
    total: projects.length,
    favorites: projects.filter(p => p.starred).length
  };

  // 语言切换
  const handleLanguageChange = useCallback((newLanguage) => {
    setLanguage(newLanguage);
    setShowLanguageDropdown(false);
  }, []);

  const handleLogin = useCallback(() => {
    navigate('/login');
  }, [navigate]);

  const handleCreateProject = useCallback(async () => {
    if (!newProject.name.trim()) return;

    setIsLoading(true);
    try {
      const response = await projectAPI.createProject({
        project_name: newProject.name.trim(),
        description: newProject.description.trim()
      });

      if (response.success) {
        setShowCreateModal(false);
        setNewProject({ name: '', description: '' });
        await loadProjects();
        // 可以添加成功提示
      } else {
        throw new Error(response.message || '创建项目失败');
      }
    } catch (error) {
      console.error('创建项目失败:', error);
      setError(error.message);
    } finally {
      setIsLoading(false);
    }
  }, [newProject, loadProjects]);

  const handleDeleteProject = useCallback(async (projectId) => {
    if (!confirm(t.deleteConfirm)) return;

    try {
      const response = await projectAPI.deleteProject(projectId);
      if (response.success) {
        await loadProjects();
      } else {
        throw new Error(response.message || '删除项目失败');
      }
    } catch (error) {
      console.error('删除项目失败:', error);
      setError(error.message);
    }
  }, [t.deleteConfirm, loadProjects]);

  const handleOpenProject = useCallback((projectId) => {
    navigate(`/ai-project/${projectId}`);
  }, [navigate]);

  const toggleProjectStar = useCallback(async (projectId) => {
    try {
      const response = await projectAPI.toggleFavorite(projectId);
      if (response.success) {
        await loadProjects();
      }
    } catch (error) {
      console.error('切换收藏状态失败:', error);
    }
  }, [loadProjects]);

  return (
    <div className="ai-analyzer-page">
      {/* Liquid Glass Floating Navigation */}
      <header className="liquid-glass-header">
        <div className="floating-nav-container">
          {/* Back to Home Button - Preserved Design Foundation */}
          <button
            className="liquid-glass-button liquid-back-button"
            onClick={() => navigate('/')}
          >
            <svg width="18" height="18" viewBox="0 0 24 24" fill="none">
              <path d="M19 12H5M12 19l-7-7 7-7" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
            <span>{t.backToHome}</span>
          </button>

          {/* Floating Title Panel */}
          <div className="liquid-glass-panel liquid-title-panel">
            <h1 className="liquid-title">{t.title}</h1>
            <p className="liquid-subtitle">{t.subtitle}</p>
          </div>

          {/* Right Side Controls */}
          <div className="header-controls">
            {/* User Badge - Only show when authenticated */}
            {isAuthenticated && currentUser && (
              <div className="liquid-glass-button liquid-user-badge">
                <div className="user-avatar">
                  {(currentUser.username || currentUser.email).charAt(0).toUpperCase()}
                </div>
                <span className="user-name">{currentUser.username || currentUser.email}</span>
              </div>
            )}

            {/* Language Switcher */}
            <div className="liquid-language-switcher" ref={dropdownRef}>
              <button
                className="liquid-glass-button liquid-lang-button"
                onClick={() => setShowLanguageDropdown(!showLanguageDropdown)}
              >
                <span className="lang-flag">{language === 'en' ? '🇺🇸' : '🇨🇳'}</span>
                <span className="lang-text">{language === 'en' ? 'EN' : '中文'}</span>
                <svg
                  className={`chevron ${showLanguageDropdown ? 'open' : ''}`}
                  width="14" height="14" viewBox="0 0 24 24" fill="none"
                >
                  <path d="M6 9l6 6 6-6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
              </button>

              {showLanguageDropdown && (
                <div className="liquid-glass-panel liquid-lang-dropdown">
                  <button
                    className={`liquid-lang-option ${language === 'en' ? 'active' : ''}`}
                    onClick={() => handleLanguageChange('en')}
                  >
                    <span className="flag">🇺🇸</span>
                    <span>English</span>
                  </button>
                  <button
                    className={`liquid-lang-option ${language === 'zh' ? 'active' : ''}`}
                    onClick={() => handleLanguageChange('zh')}
                  >
                    <span className="flag">🇨🇳</span>
                    <span>中文</span>
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      </header>

      {/* Main Content Area - Liquid Glass Design */}
      <main className="liquid-main-container">
        <div className="liquid-content-wrapper">
          {/* Unauthenticated State - Liquid Glass Welcome */}
          {!isAuthenticated && (
            <div className="liquid-welcome-section">
              <div className="liquid-glass-panel liquid-welcome-card">
                <div className="welcome-icon-container">
                  <div className="liquid-welcome-icon">
                    <svg width="80" height="80" viewBox="0 0 24 24" fill="none">
                      <path d="M13 2L3 14h9l-1 8 10-12h-9l1-8z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                  </div>
                </div>
                <h2 className="liquid-welcome-title">{t.loginRequired}</h2>
                <p className="liquid-welcome-desc">{t.loginToCreateProjects}</p>
                <button className="liquid-glass-button liquid-login-button" onClick={handleLogin}>
                  <span>{t.loginButton}</span>
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                    <path d="M5 12h14M12 5l7 7-7 7" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                </button>
              </div>
            </div>
          )}

          {/* Authenticated State - Redesigned Professional Layout */}
          {isAuthenticated && (
            <>
              {/* Enhanced Professional Control Section */}
              <div className="liquid-control-section">
                <div className="liquid-glass-panel control-header-panel">
                  <div className="control-header-content">
                    <div className="section-title-group">
                      <h2 className="section-title">Project Management</h2>
                      <p className="section-subtitle">Create and manage your industrial site analysis projects</p>
                    </div>

                    <div className="primary-actions-group">
                      <button
                        className="liquid-glass-button primary-create-button"
                        onClick={() => setShowCreateModal(true)}
                      >
                        <div className="button-icon-wrapper">
                          <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                            <path d="M12 5v14M5 12h14" stroke="currentColor" strokeWidth="2.5" strokeLinecap="round" strokeLinejoin="round"/>
                          </svg>
                        </div>
                        <span className="button-text">{t.createProject}</span>
                        <div className="button-glow"></div>
                      </button>
                    </div>
                  </div>
                </div>

                {/* Enhanced View Switcher with Better Integration */}
                <div className="liquid-glass-panel view-control-panel">
                  <div className="view-switcher-enhanced">
                    <div className="switcher-label">
                      <span>View:</span>
                    </div>
                    <div className="switcher-options-container">
                      <button
                        className={`liquid-switcher-option enhanced ${activeTab === 'all' ? 'active' : ''}`}
                        onClick={() => setActiveTab('all')}
                      >
                        <div className="option-icon">
                          <svg width="18" height="18" viewBox="0 0 24 24" fill="none">
                            <path d="M3 7h18M3 12h18M3 17h18" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                          </svg>
                        </div>
                        <span className="option-label">{t.allProjects}</span>
                        <div className="option-count">{projectStats.total}</div>
                      </button>

                      <button
                        className={`liquid-switcher-option enhanced ${activeTab === 'favorites' ? 'active' : ''}`}
                        onClick={() => setActiveTab('favorites')}
                      >
                        <div className="option-icon">
                          <svg width="18" height="18" viewBox="0 0 24 24" fill="none">
                            <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                          </svg>
                        </div>
                        <span className="option-label">{t.favorites}</span>
                        <div className="option-count">{projectStats.favorites}</div>
                      </button>

                      <div className={`liquid-switcher-indicator enhanced ${activeTab}`}></div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Error Message - Liquid Glass */}
              {error && (
                <div className="liquid-glass-panel liquid-error-banner">
                  <div className="error-content">
                    <svg className="error-icon" width="20" height="20" viewBox="0 0 24 24" fill="none">
                      <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="2"/>
                      <line x1="15" y1="9" x2="9" y2="15" stroke="currentColor" strokeWidth="2"/>
                      <line x1="9" y1="9" x2="15" y2="15" stroke="currentColor" strokeWidth="2"/>
                    </svg>
                    <span>{error}</span>
                  </div>
                  <button className="liquid-glass-button liquid-retry-button" onClick={loadProjects}>
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                      <path d="M23 4v6h-6M1 20v-6h6M20.49 9A9 9 0 0 0 5.64 5.64L1 10m22 4a9 9 0 0 1-14.85 4.36L3 14" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                    {t.retryButton}
                  </button>
                </div>
              )}

              {/* Projects Section - Liquid Glass Grid */}
              <div className="liquid-projects-section">
                {isLoading ? (
                  <div className="liquid-glass-panel liquid-loading-container">
                    <div className="liquid-loading-spinner">
                      <div className="spinner-ring"></div>
                      <div className="spinner-ring"></div>
                      <div className="spinner-ring"></div>
                    </div>
                    <p className="loading-text">{t.loadingProjects}</p>
                  </div>
                ) : displayedProjects.length === 0 ? (
                  <div className="liquid-glass-panel liquid-empty-container">
                    <div className="empty-illustration">
                      <div className="liquid-empty-icon">
                        <svg width="120" height="120" viewBox="0 0 24 24" fill="none">
                          <path d="M22 19a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h5l2 3h9a2 2 0 0 1 2 2z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                          <circle cx="12" cy="14" r="3" stroke="currentColor" strokeWidth="1.5" strokeDasharray="3 3"/>
                        </svg>
                      </div>
                    </div>
                    <h3 className="liquid-empty-title">
                      {activeTab === 'favorites' ? t.noFavorites : t.noProjects}
                    </h3>
                    <p className="liquid-empty-desc">
                      {activeTab === 'favorites' ? t.noFavoritesDesc : t.noProjectsDesc}
                    </p>
                    {activeTab !== 'favorites' && (
                      <button className="liquid-glass-button liquid-empty-action-button" onClick={() => setShowCreateModal(true)}>
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                          <path d="M12 5v14M5 12h14" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                        </svg>
                        {t.createProject}
                      </button>
                    )}
                  </div>
                ) : (
                  <div className="liquid-projects-grid">
                    {displayedProjects.map((project, index) => (
                      <div
                        key={project.id}
                        className="liquid-glass-panel liquid-project-card"
                        style={{
                          animationDelay: `${index * 0.1}s`
                        }}
                        onClick={() => handleOpenProject(project.id)}
                      >
                        <div className="liquid-card-header">
                          <div className="project-info">
                            <h3 className="liquid-project-name">{project.name}</h3>
                            <div className="project-meta">
                              <span className="creation-date">
                                {new Date(project.createdAt).toLocaleDateString()}
                              </span>
                              <div className={`liquid-status-badge status-${project.status}`}>
                                {project.status === 'completed' ? t.statusCompleted :
                                 project.status === 'in-progress' ? t.statusInProgress : t.statusActive}
                              </div>
                            </div>
                          </div>
                          <button
                            className={`liquid-glass-button liquid-star-button ${project.starred ? 'starred' : ''}`}
                            onClick={(e) => {
                              e.stopPropagation();
                              toggleProjectStar(project.id);
                            }}
                          >
                            <svg width="18" height="18" viewBox="0 0 24 24" fill="none">
                              <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                            </svg>
                          </button>
                        </div>

                        {project.description && (
                          <p className="liquid-project-desc">{project.description}</p>
                        )}

                        <div className="liquid-card-actions">
                          <button
                            className="liquid-glass-button liquid-action-button liquid-open-button"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleOpenProject(project.id);
                            }}
                          >
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                              <path d="M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                              <path d="M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                            </svg>
                            {t.openProject}
                          </button>
                          <button
                            className="liquid-glass-button liquid-action-button liquid-delete-button"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleDeleteProject(project.id);
                            }}
                          >
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                              <path d="M3 6h18M8 6V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2m3 0v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6h14z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                            </svg>
                            {t.deleteProject}
                          </button>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </>
          )}
        </div>
      </main>

      {/* Create Project Modal - Liquid Glass */}
      {showCreateModal && (
        <div className="liquid-modal-backdrop">
          <div className="liquid-glass-panel liquid-create-modal">
            <div className="liquid-modal-header">
              <h2 className="liquid-modal-title">{t.createProject}</h2>
              <button
                className="liquid-glass-button liquid-close-button"
                onClick={() => {
                  setShowCreateModal(false);
                  setNewProject({ name: '', description: '' });
                }}
              >
                <svg width="18" height="18" viewBox="0 0 24 24" fill="none">
                  <line x1="18" y1="6" x2="6" y2="18" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  <line x1="6" y1="6" x2="18" y2="18" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
              </button>
            </div>

            <div className="liquid-modal-body">
              <div className="liquid-input-group">
                <label className="liquid-input-label">{t.projectName}</label>
                <input
                  type="text"
                  className="liquid-text-input"
                  value={newProject.name}
                  onChange={(e) => setNewProject(prev => ({ ...prev, name: e.target.value }))}
                  placeholder="Enter project name..."
                  autoFocus
                />
              </div>

              <div className="liquid-input-group">
                <label className="liquid-input-label">{t.projectDescription}</label>
                <textarea
                  className="liquid-textarea-input"
                  value={newProject.description}
                  onChange={(e) => setNewProject(prev => ({ ...prev, description: e.target.value }))}
                  placeholder="Enter project description (optional)..."
                  rows="3"
                />
              </div>
            </div>

            <div className="liquid-modal-footer">
              <button
                className="liquid-glass-button liquid-cancel-button"
                onClick={() => {
                  setShowCreateModal(false);
                  setNewProject({ name: '', description: '' });
                }}
              >
                {t.cancel}
              </button>
              <button
                className="liquid-glass-button liquid-submit-button"
                onClick={handleCreateProject}
                disabled={!newProject.name.trim() || isLoading}
              >
                {isLoading ? (
                  <>
                    <div className="liquid-btn-spinner"></div>
                    {t.creating}
                  </>
                ) : (
                  <>
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                      <path d="M12 5v14M5 12h14" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                    {t.create}
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AIAnalyzerPage;
