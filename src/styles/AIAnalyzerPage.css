/* ===== AI Analyzer Page - Liquid Glass Aesthetic Foundation ===== */

/* Enhanced CSS Custom Properties for Professional Liquid Glass System */
:root {
  /* Enhanced Liquid Glass Colors - Professional Grade */
  --liquid-glass-bg: rgba(255, 255, 255, 0.08);
  --liquid-glass-bg-strong: rgba(255, 255, 255, 0.12);
  --liquid-glass-border: rgba(255, 255, 255, 0.18);
  --liquid-glass-border-strong: rgba(255, 255, 255, 0.25);
  --liquid-glass-highlight: rgba(255, 255, 255, 0.15);
  --liquid-glass-shadow: rgba(0, 0, 0, 0.25);
  --liquid-glass-shadow-strong: rgba(0, 0, 0, 0.4);

  /* Enhanced Cinematic Dark Theme */
  --dark-bg-primary: #0a0f1c;
  --dark-bg-secondary: #0f1419;
  --dark-bg-tertiary: #161b22;
  --dark-surface: rgba(22, 31, 49, 0.9);
  --dark-surface-elevated: rgba(30, 41, 59, 0.95);

  /* Professional Cyan/Blue Accents */
  --accent-cyan: #00d4ff;
  --accent-cyan-bright: #1ae7ff;
  --accent-blue: #3b82f6;
  --accent-blue-bright: #60a5fa;
  --accent-cyan-glow: rgba(0, 212, 255, 0.35);
  --accent-blue-glow: rgba(59, 130, 246, 0.35);
  --accent-cyan-glow-strong: rgba(0, 212, 255, 0.5);
  --accent-blue-glow-strong: rgba(59, 130, 246, 0.5);

  /* Enhanced Text Colors */
  --text-primary: #f8fafc;
  --text-secondary: #cbd5e1;
  --text-tertiary: #94a3b8;
  --text-accent: #60a5fa;
  --text-accent-bright: #38bdf8;

  /* Professional Animation Durations */
  --liquid-duration-fast: 0.15s;
  --liquid-duration-normal: 0.3s;
  --liquid-duration-slow: 0.6s;
  --liquid-duration-extra-slow: 0.8s;

  /* Enhanced Glass Effects */
  --glass-blur: blur(24px);
  --glass-blur-strong: blur(40px);
  --glass-blur-subtle: blur(12px);
  --glass-saturation: saturate(200%);
  --glass-brightness: brightness(1.1);

  /* Professional Depth Layers */
  --depth-1: 0 2px 8px rgba(0, 0, 0, 0.08);
  --depth-2: 0 4px 16px rgba(0, 0, 0, 0.12);
  --depth-3: 0 8px 32px rgba(0, 0, 0, 0.16);
  --depth-4: 0 16px 64px rgba(0, 0, 0, 0.2);
}

.ai-analyzer-page {
  min-height: 100vh;
  position: relative;
  background: var(--dark-bg-primary);
  color: var(--text-primary);
  font-family: 'Inter', system-ui, sans-serif;
  overflow-x: hidden;
  z-index: 0;

  /* Enhanced cinematic background */
  background-image:
    radial-gradient(circle at 20% 20%, rgba(0, 212, 255, 0.08) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(59, 130, 246, 0.06) 0%, transparent 50%),
    linear-gradient(135deg, var(--dark-bg-primary) 0%, var(--dark-bg-secondary) 100%);
}

/* ===== Core Liquid Glass Components ===== */

/* Enhanced Professional Liquid Glass Panel */
.liquid-glass-panel {
  background: var(--liquid-glass-bg);
  backdrop-filter: var(--glass-blur) var(--glass-saturation) var(--glass-brightness);
  -webkit-backdrop-filter: var(--glass-blur) var(--glass-saturation) var(--glass-brightness);
  border: 1px solid var(--liquid-glass-border);
  border-radius: 20px;
  box-shadow:
    var(--depth-3),
    0 4px 16px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 var(--liquid-glass-highlight),
    inset 0 -1px 0 rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
  transition: all var(--liquid-duration-normal) cubic-bezier(0.4, 0, 0.2, 1);

  /* Enhanced professional appearance */
  isolation: isolate;
}

/* Enhanced Liquid Glass Distortion Effect */
.liquid-glass-panel::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.12) 0%,
    rgba(255, 255, 255, 0.08) 30%,
    rgba(255, 255, 255, 0.04) 60%,
    rgba(255, 255, 255, 0.02) 100%);
  border-radius: inherit;
  pointer-events: none;
  z-index: 1;

  /* Subtle animation for living glass effect */
  animation: glass-shimmer 8s ease-in-out infinite alternate;
}

/* Professional Glass Shimmer Animation */
@keyframes glass-shimmer {
  0% {
    opacity: 0.8;
    background: linear-gradient(135deg,
      rgba(255, 255, 255, 0.12) 0%,
      rgba(255, 255, 255, 0.08) 30%,
      rgba(255, 255, 255, 0.04) 60%,
      rgba(255, 255, 255, 0.02) 100%);
  }
  100% {
    opacity: 1;
    background: linear-gradient(135deg,
      rgba(255, 255, 255, 0.15) 0%,
      rgba(255, 255, 255, 0.1) 30%,
      rgba(255, 255, 255, 0.06) 60%,
      rgba(255, 255, 255, 0.03) 100%);
  }
}

/* Enhanced Content Layering */
.liquid-glass-panel > * {
  position: relative;
  z-index: 2;
}

/* Hover Enhancement for Interactive Panels */
.liquid-glass-panel:hover {
  border-color: var(--liquid-glass-border-strong);
  box-shadow:
    var(--depth-4),
    0 6px 20px rgba(0, 0, 0, 0.18),
    inset 0 1px 0 rgba(255, 255, 255, 0.2),
    inset 0 -1px 0 rgba(0, 0, 0, 0.12);

  /* Enhanced hover glow */
  filter: brightness(1.02);
}

/* Professional Enhanced Liquid Glass Button Base */
.liquid-glass-button {
  background: var(--liquid-glass-bg);
  backdrop-filter: var(--glass-blur-subtle) var(--glass-saturation) var(--glass-brightness);
  -webkit-backdrop-filter: var(--glass-blur-subtle) var(--glass-saturation) var(--glass-brightness);
  border: 1px solid var(--liquid-glass-border);
  border-radius: 50px; /* Professional elongated capsule shape */
  color: var(--text-primary);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all var(--liquid-duration-normal) cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px 24px;
  text-decoration: none;
  border: none;
  outline: none;

  /* Professional enhanced glass shadow */
  box-shadow:
    var(--depth-2),
    0 2px 8px rgba(0, 0, 0, 0.08),
    inset 0 1px 0 var(--liquid-glass-highlight),
    inset 0 -1px 0 rgba(0, 0, 0, 0.05);

  /* Professional spacing and alignment */
  min-height: 44px;
  white-space: nowrap;
  isolation: isolate;

  /* Enhanced typography */
  letter-spacing: 0.025em;
  font-family: 'Inter', system-ui, sans-serif;
}

/* Liquid glass button distortion layer */
.liquid-glass-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.08) 0%,
    rgba(255, 255, 255, 0.04) 50%,
    rgba(255, 255, 255, 0.02) 100%);
  border-radius: inherit;
  pointer-events: none;
  z-index: 1;
}

/* Button content above glass effect */
.liquid-glass-button > * {
  position: relative;
  z-index: 2;
}

/* Professional Enhanced Button Hover State */
.liquid-glass-button:hover {
  transform: translateY(-3px) scale(1.02);
  background: var(--liquid-glass-bg-strong);
  border-color: var(--liquid-glass-border-strong);
  box-shadow:
    var(--depth-3),
    0 6px 20px rgba(0, 0, 0, 0.15),
    0 3px 10px var(--accent-blue-glow),
    0 0 25px rgba(59, 130, 246, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.2),
    inset 0 -1px 0 rgba(0, 0, 0, 0.08);

  /* Professional glow effect */
  filter: brightness(1.08) saturate(1.1);

  /* Enhanced backdrop effect */
  backdrop-filter: var(--glass-blur) var(--glass-saturation) var(--glass-brightness);
  -webkit-backdrop-filter: var(--glass-blur) var(--glass-saturation) var(--glass-brightness);
}

/* Professional Active State */
.liquid-glass-button:active {
  transform: translateY(-1px) scale(1.01);
  transition-duration: var(--liquid-duration-fast);
}

/* Focus State for Accessibility */
.liquid-glass-button:focus-visible {
  outline: 2px solid var(--accent-blue);
  outline-offset: 2px;
  box-shadow:
    var(--depth-2),
    0 0 0 4px rgba(59, 130, 246, 0.2),
    inset 0 1px 0 var(--liquid-glass-highlight);
}

/* Organic cursor interaction enhancement */
.liquid-glass-button,
.liquid-glass-panel {
  cursor: pointer;
  transition: all var(--liquid-duration-normal) cubic-bezier(0.23, 1, 0.32, 1);
}

/* Ensure language dropdown visibility */
.ai-analyzer-page .language-dropdown,
.ai-analyzer-page .language-dropdown-trigger {
  display: flex !important;
  visibility: visible !important;
  opacity: 1 !important;
}

/* ===== Liquid Glass Header Navigation ===== */

.liquid-glass-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 50;
  padding: 1.5rem 2rem;
  pointer-events: none; /* Allow clicks to pass through container */
}

.floating-nav-container {
  display: grid;
  grid-template-columns: auto 1fr auto;
  grid-template-areas: "back title controls";
  align-items: flex-start;
  gap: 2rem;
  max-width: 1400px;
  margin: 0 auto;
}

.floating-nav-container > * {
  pointer-events: auto; /* Restore pointer events for child elements */
}

/* Back Button - Preserved Foundation Design */
.liquid-back-button {
  grid-area: back;
  background: var(--liquid-glass-bg);
  border-color: var(--accent-cyan);
  color: var(--text-primary);
  min-width: 140px;
  font-weight: 500;
}

.liquid-back-button:hover {
  border-color: var(--accent-cyan);
  box-shadow:
    0 8px 24px rgba(0, 0, 0, 0.2),
    0 4px 12px var(--accent-cyan-glow),
    0 0 20px var(--accent-cyan-glow),
    inset 0 1px 0 rgba(255, 255, 255, 0.15);
  color: var(--accent-cyan);
}

/* Floating Title Panel */
.liquid-title-panel {
  grid-area: title;
  justify-self: center;
  padding: 1rem 2rem;
  text-align: center;
  min-width: 300px;
  backdrop-filter: var(--glass-blur-strong) var(--glass-saturation);
}

.liquid-title {
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0 0 0.25rem 0;
  background: linear-gradient(90deg, var(--accent-cyan), var(--accent-blue));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 0 30px var(--accent-blue-glow);
  white-space: nowrap;
}

.liquid-subtitle {
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin: 0;
  white-space: nowrap;
}

/* Header Controls */
.header-controls {
  grid-area: controls;
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  justify-self: end;
}

/* User Badge */
.liquid-user-badge {
  background: var(--liquid-glass-bg);
  border-color: var(--accent-blue);
  color: var(--text-primary);
  min-width: 160px;
  justify-content: flex-start;
}

.liquid-user-badge .user-avatar {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--accent-cyan), var(--accent-blue));
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  font-weight: 600;
  color: white;
  flex-shrink: 0;
}

.liquid-user-badge .user-name {
  font-size: 0.875rem;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 120px;
}

/* Language Switcher */
.liquid-language-switcher {
  position: relative;
}

.liquid-lang-button {
  background: var(--liquid-glass-bg);
  border-color: rgba(147, 51, 234, 0.3);
  color: var(--text-primary);
  min-width: 100px;
  justify-content: space-between;
}

.liquid-lang-button:hover {
  border-color: rgba(147, 51, 234, 0.5);
  box-shadow:
    0 8px 24px rgba(0, 0, 0, 0.2),
    0 4px 12px rgba(147, 51, 234, 0.3),
    0 0 20px rgba(147, 51, 234, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.15);
}

.liquid-lang-button .chevron {
  transition: transform var(--liquid-duration-normal) ease;
  color: var(--text-secondary);
}

.liquid-lang-button .chevron.open {
  transform: rotate(180deg);
}

/* Language Dropdown */
.liquid-lang-dropdown {
  position: absolute;
  top: calc(100% + 0.75rem);
  right: 0;
  min-width: 140px;
  padding: 0.5rem;
  z-index: 100;
  animation: liquid-dropdown-appear var(--liquid-duration-normal) cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes liquid-dropdown-appear {
  from {
    opacity: 0;
    transform: translateY(-10px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.liquid-lang-option {
  width: 100%;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  background: transparent;
  border: none;
  color: var(--text-primary);
  font-size: 0.875rem;
  cursor: pointer;
  transition: all var(--liquid-duration-fast) ease;
  border-radius: 8px;
  text-align: left;
  position: relative;
}

.liquid-lang-option::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background: transparent;
  border-radius: 0 2px 2px 0;
  transition: all var(--liquid-duration-fast) ease;
}

.liquid-lang-option:hover {
  background: rgba(59, 130, 246, 0.1);
  color: var(--accent-blue);
}

.liquid-lang-option:hover::before {
  background: var(--accent-blue);
}

.liquid-lang-option.active {
  background: rgba(37, 99, 235, 0.15);
  color: var(--accent-blue);
}

.liquid-lang-option.active::before {
  background: var(--accent-blue);
}

/* ===== Main Content Area - Liquid Glass Design ===== */

.liquid-main-container {
  padding: 8rem 2rem 2rem;
  min-height: 100vh;
  position: relative;
  z-index: 1;

  /* Enhanced professional spacing */
  box-sizing: border-box;
}

.liquid-content-wrapper {
  max-width: 1400px;
  margin: 0 auto;

  /* Improved content organization and spacing */
  display: flex;
  flex-direction: column;
  gap: 0; /* Remove gap to maintain existing spacing */
}

/* Welcome Section - Unauthenticated State */
.liquid-welcome-section {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 60vh;
  padding: 2rem;
}

.liquid-welcome-card {
  max-width: 500px;
  padding: 3rem 2rem;
  text-align: center;
  backdrop-filter: var(--glass-blur-strong) var(--glass-saturation);
}

.welcome-icon-container {
  margin-bottom: 2rem;
}

.liquid-welcome-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 120px;
  height: 120px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--accent-cyan), var(--accent-blue));
  color: white;
  margin-bottom: 1rem;
  animation: liquid-float 3s ease-in-out infinite;
}

@keyframes liquid-float {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-10px); }
}

.liquid-welcome-title {
  font-size: 2rem;
  font-weight: 700;
  margin: 0 0 1rem 0;
  background: linear-gradient(90deg, var(--accent-cyan), var(--accent-blue));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.liquid-welcome-desc {
  color: var(--text-secondary);
  margin: 0 0 2.5rem 0;
  font-size: 1.125rem;
  line-height: 1.6;
}

.liquid-login-button {
  background: linear-gradient(135deg, var(--accent-cyan), var(--accent-blue));
  border: none;
  color: white;
  font-weight: 600;
  padding: 14px 28px;
  font-size: 1rem;
}

.liquid-login-button:hover {
  transform: translateY(-2px) scale(1.05);
  box-shadow:
    0 12px 32px rgba(0, 0, 0, 0.3),
    0 6px 16px var(--accent-cyan-glow),
    0 0 24px var(--accent-blue-glow);
}

/* ===== Professional Control Section - Enhanced Layout ===== */

.liquid-control-section {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  margin-bottom: 3rem;
}

/* Control Header Panel - Professional Information Architecture */
.control-header-panel {
  padding: 2rem 2.5rem;
  backdrop-filter: var(--glass-blur-strong) var(--glass-saturation);
  border: 1px solid rgba(255, 255, 255, 0.12);

  /* Enhanced professional glass effect */
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.08) 0%,
    rgba(255, 255, 255, 0.04) 50%,
    rgba(255, 255, 255, 0.02) 100%);

  /* Professional shadow hierarchy */
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.12),
    0 4px 16px rgba(0, 0, 0, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.1),
    inset 0 -1px 0 rgba(0, 0, 0, 0.1);
}

.control-header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 2rem;
}

/* Section Title Group - Enhanced Typography Hierarchy */
.section-title-group {
  flex: 1;
}

.section-title {
  font-size: 1.75rem;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
  background: linear-gradient(135deg, var(--accent-cyan), var(--accent-blue));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 0 30px var(--accent-blue-glow);
  letter-spacing: -0.025em;
}

.section-subtitle {
  font-size: 1rem;
  color: var(--text-secondary);
  margin: 0;
  line-height: 1.5;
  opacity: 0.9;
}

/* Primary Actions Group - Enhanced Button Design */
.primary-actions-group {
  display: flex;
  align-items: center;
  gap: 1rem;
}

/* Enhanced Primary Create Button - Professional Design */
.primary-create-button {
  background: linear-gradient(135deg, var(--accent-cyan), var(--accent-blue));
  border: none;
  color: white;
  font-weight: 600;
  padding: 16px 32px;
  font-size: 1rem;
  min-width: 200px;
  position: relative;
  overflow: hidden;

  /* Professional enhanced appearance */
  box-shadow:
    0 6px 20px rgba(0, 0, 0, 0.15),
    0 3px 10px var(--accent-cyan-glow),
    0 0 30px rgba(59, 130, 246, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);

  /* Enhanced spacing and alignment */
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;

  /* Professional border radius */
  border-radius: 50px;

  /* Enhanced typography */
  letter-spacing: 0.025em;
  text-transform: uppercase;
  font-size: 0.875rem;
}

.primary-create-button .button-icon-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  flex-shrink: 0;
}

.primary-create-button .button-text {
  font-weight: 600;
  flex-shrink: 0;
}

.primary-create-button .button-glow {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.2) 0%,
    rgba(255, 255, 255, 0.1) 50%,
    rgba(255, 255, 255, 0.05) 100%);
  border-radius: inherit;
  pointer-events: none;
  opacity: 0;
  transition: opacity var(--liquid-duration-normal) ease;
}

.primary-create-button:hover {
  transform: translateY(-3px) scale(1.02);
  box-shadow:
    0 12px 32px rgba(0, 0, 0, 0.2),
    0 6px 16px var(--accent-cyan-glow),
    0 0 40px rgba(59, 130, 246, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.4);

  /* Enhanced professional glow */
  filter: brightness(1.1) saturate(1.2);
}

.primary-create-button:hover .button-glow {
  opacity: 1;
}

.primary-create-button:active {
  transform: translateY(-1px) scale(1.01);
}

/* ===== Enhanced View Control Panel - Professional Design ===== */

.view-control-panel {
  padding: 1.5rem 2rem;
  backdrop-filter: var(--glass-blur) var(--glass-saturation);
  border: 1px solid rgba(255, 255, 255, 0.1);

  /* Professional glass effect */
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.06) 0%,
    rgba(255, 255, 255, 0.03) 50%,
    rgba(255, 255, 255, 0.01) 100%);

  /* Enhanced shadow */
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.08),
    0 2px 8px rgba(0, 0, 0, 0.04),
    inset 0 1px 0 rgba(255, 255, 255, 0.08);
}

.view-switcher-enhanced {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.switcher-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  flex-shrink: 0;
}

.switcher-options-container {
  display: flex;
  gap: 0.75rem;
  position: relative;
  align-items: center;
  flex: 1;
}

/* Enhanced Switcher Options - Professional Design */
.liquid-switcher-option.enhanced {
  background: rgba(255, 255, 255, 0.04);
  border: 1px solid rgba(255, 255, 255, 0.12);
  border-radius: 50px;
  color: var(--text-secondary);
  padding: 1rem 1.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all var(--liquid-duration-normal) cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  gap: 0.75rem;
  position: relative;
  z-index: 2;
  min-width: 160px;
  justify-content: center;

  /* Professional enhanced appearance */
  box-shadow:
    0 3px 12px rgba(0, 0, 0, 0.08),
    0 1px 4px rgba(0, 0, 0, 0.04),
    inset 0 1px 0 rgba(255, 255, 255, 0.08);

  /* Enhanced backdrop effect */
  backdrop-filter: blur(8px) saturate(120%);
  -webkit-backdrop-filter: blur(8px) saturate(120%);
}

.liquid-switcher-option.enhanced .option-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  width: 20px;
  height: 20px;
}

.liquid-switcher-option.enhanced .option-label {
  font-weight: 500;
  letter-spacing: 0.025em;
}

.liquid-switcher-option.enhanced .option-count {
  background: rgba(255, 255, 255, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 14px;
  padding: 0.375rem 0.75rem;
  font-size: 0.75rem;
  font-weight: 600;
  min-width: 28px;
  text-align: center;
  flex-shrink: 0;

  /* Enhanced count badge */
  box-shadow:
    0 2px 6px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.liquid-switcher-option.enhanced:hover {
  color: var(--text-primary);
  border-color: rgba(255, 255, 255, 0.25);
  background: rgba(255, 255, 255, 0.08);

  /* Enhanced hover effect */
  transform: translateY(-2px) scale(1.02);
  box-shadow:
    0 6px 20px rgba(0, 0, 0, 0.12),
    0 3px 8px rgba(0, 0, 0, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.12);
}

.liquid-switcher-option.enhanced.active {
  color: var(--accent-blue);
  border-color: var(--accent-blue);
  background: rgba(59, 130, 246, 0.12);

  /* Enhanced active state */
  box-shadow:
    0 6px 20px rgba(59, 130, 246, 0.2),
    0 3px 8px rgba(59, 130, 246, 0.1),
    0 0 30px rgba(59, 130, 246, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.15);

  /* Active glow effect */
  filter: brightness(1.05);
}

.liquid-switcher-option.enhanced.active .option-count {
  background: var(--accent-blue);
  border-color: var(--accent-blue);
  color: white;

  /* Enhanced active count */
  box-shadow:
    0 3px 8px rgba(59, 130, 246, 0.3),
    0 0 15px rgba(59, 130, 246, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

/* Enhanced Switcher Indicator - Professional Animation */
.liquid-switcher-indicator.enhanced {
  position: absolute;
  top: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    var(--accent-cyan),
    var(--accent-blue),
    rgba(59, 130, 246, 0.8));
  border-radius: 50px;
  transition: all var(--liquid-duration-slow) cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 1;
  opacity: 0.15;

  /* Enhanced glow effect */
  box-shadow:
    0 0 20px rgba(59, 130, 246, 0.3),
    0 0 40px rgba(0, 212, 255, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);

  /* Subtle animation */
  filter: blur(0.5px);
}

.liquid-switcher-indicator.enhanced.all {
  left: 0;
  width: 160px;
}

.liquid-switcher-indicator.enhanced.favorites {
  left: calc(160px + 0.75rem);
  width: 160px;
}

/* Enhanced indicator glow animation */
.liquid-switcher-indicator.enhanced::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(135deg, var(--accent-cyan), var(--accent-blue));
  border-radius: inherit;
  z-index: -1;
  opacity: 0.3;
  filter: blur(4px);
  animation: indicator-pulse 3s ease-in-out infinite alternate;
}

@keyframes indicator-pulse {
  0% {
    opacity: 0.2;
    transform: scale(1);
  }
  100% {
    opacity: 0.4;
    transform: scale(1.02);
  }
}

/* ===== Project Cards - Liquid Glass Design ===== */

/* Error Banner */
.liquid-error-banner {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  border-color: rgba(239, 68, 68, 0.4);
  background: rgba(239, 68, 68, 0.1);
}

.liquid-error-banner .error-content {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: #fca5a5;
}

.liquid-retry-button {
  background: rgba(239, 68, 68, 0.2);
  border-color: rgba(239, 68, 68, 0.4);
  color: #fca5a5;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
}

.liquid-retry-button:hover {
  background: rgba(239, 68, 68, 0.3);
  border-color: rgba(239, 68, 68, 0.6);
  box-shadow:
    0 4px 12px rgba(239, 68, 68, 0.3),
    0 0 20px rgba(239, 68, 68, 0.2);
}

/* Projects Section */
.liquid-projects-section {
  min-height: 400px;
  position: relative;
}

/* Loading State */
.liquid-loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  text-align: center;
  backdrop-filter: var(--glass-blur-strong) var(--glass-saturation);
}

.liquid-loading-spinner {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
}

.spinner-ring {
  width: 12px;
  height: 12px;
  border: 2px solid rgba(59, 130, 246, 0.3);
  border-top: 2px solid var(--accent-blue);
  border-radius: 50%;
  animation: liquid-spin 1s linear infinite;
}

.spinner-ring:nth-child(2) {
  animation-delay: 0.1s;
}

.spinner-ring:nth-child(3) {
  animation-delay: 0.2s;
}

@keyframes liquid-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  color: var(--text-secondary);
  margin: 0;
  font-size: 0.875rem;
}

/* Empty State */
.liquid-empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  text-align: center;
  backdrop-filter: var(--glass-blur-strong) var(--glass-saturation);
}

.liquid-empty-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 120px;
  height: 120px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--accent-cyan), var(--accent-blue));
  color: white;
  margin-bottom: 1.5rem;
  animation: liquid-float 3s ease-in-out infinite;
}

.liquid-empty-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0 0 1rem 0;
  background: linear-gradient(90deg, var(--accent-cyan), var(--accent-blue));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.liquid-empty-desc {
  color: var(--text-secondary);
  margin: 0 0 2.5rem 0;
  max-width: 450px;
  font-size: 1rem;
  line-height: 1.6;
}

.liquid-empty-action-button {
  background: linear-gradient(135deg, var(--accent-cyan), var(--accent-blue));
  border: none;
  color: white;
  font-weight: 600;
  padding: 12px 24px;
}

/* Projects Grid */
.liquid-projects-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 1.5rem;
  animation: liquid-grid-appear 0.6s ease-out;
}

@keyframes liquid-grid-appear {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Enhanced Project Card - Professional Design */
.liquid-project-card {
  padding: 1.75rem;
  cursor: pointer;
  transition: all var(--liquid-duration-normal) cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: var(--glass-blur) var(--glass-saturation);
  display: flex;
  flex-direction: column;
  height: 100%;
  animation: liquid-card-appear 0.6s ease-out forwards;
  opacity: 0;

  /* Enhanced professional appearance */
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.1),
    0 2px 8px rgba(0, 0, 0, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.08);

  /* Consistent spacing and alignment */
  gap: 1rem;
}

@keyframes liquid-card-appear {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.liquid-project-card:hover {
  transform: translateY(-4px) scale(1.02);
  border-color: var(--accent-blue);
  box-shadow:
    0 12px 32px rgba(0, 0, 0, 0.2),
    0 6px 16px var(--accent-blue-glow),
    0 0 24px var(--accent-cyan-glow),
    inset 0 1px 0 rgba(255, 255, 255, 0.15);

  /* Enhanced professional hover effect */
  filter: brightness(1.02);
}

/* Card Header */
.liquid-card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
  gap: 1rem;
}

.liquid-project-name {
  color: var(--text-primary);
  margin: 0 0 0.5rem 0;
  font-size: 1.25rem;
  font-weight: 600;
  flex: 1;
  line-height: 1.3;
}

.project-meta {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  flex-wrap: wrap;
}

.creation-date {
  color: var(--text-secondary);
  font-size: 0.75rem;
}

.liquid-status-badge {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  padding: 0.25rem 0.75rem;
  font-size: 0.75rem;
  font-weight: 500;
  color: var(--text-secondary);
}

.liquid-status-badge.status-completed {
  background: rgba(34, 197, 94, 0.2);
  border-color: rgba(34, 197, 94, 0.4);
  color: #4ade80;
}

.liquid-status-badge.status-in-progress {
  background: rgba(251, 191, 36, 0.2);
  border-color: rgba(251, 191, 36, 0.4);
  color: #fbbf24;
}

.liquid-status-badge.status-active {
  background: rgba(59, 130, 246, 0.2);
  border-color: rgba(59, 130, 246, 0.4);
  color: var(--accent-blue);
}

/* Star Button */
.liquid-star-button {
  padding: 0.5rem;
  min-width: 40px;
  min-height: 40px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.1);
  color: var(--text-secondary);
  flex-shrink: 0;
}

.liquid-star-button:hover {
  color: #fbbf24;
  background: rgba(251, 191, 36, 0.2);
  border-color: rgba(251, 191, 36, 0.4);
  box-shadow:
    0 4px 12px rgba(251, 191, 36, 0.3),
    0 0 20px rgba(251, 191, 36, 0.2);
}

.liquid-star-button.starred {
  color: #fbbf24;
  background: rgba(251, 191, 36, 0.2);
  border-color: rgba(251, 191, 36, 0.4);
  box-shadow:
    0 4px 12px rgba(251, 191, 36, 0.3),
    0 0 20px rgba(251, 191, 36, 0.2);
}

/* Project Description */
.liquid-project-desc {
  color: var(--text-secondary);
  margin: 0 0 1.5rem 0;
  font-size: 0.875rem;
  line-height: 1.5;
  flex: 1;
}

/* Card Actions */
.liquid-card-actions {
  display: flex;
  gap: 0.75rem;
  margin-top: auto;
  padding-top: 1rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.liquid-action-button {
  flex: 1;
  padding: 0.75rem 1rem;
  font-size: 0.875rem;
  font-weight: 500;
  border-radius: 50px;
}

.liquid-open-button {
  background: rgba(59, 130, 246, 0.2);
  border-color: rgba(59, 130, 246, 0.4);
  color: var(--accent-blue);
}

.liquid-open-button:hover {
  background: rgba(59, 130, 246, 0.3);
  border-color: rgba(59, 130, 246, 0.6);
  box-shadow:
    0 4px 12px rgba(59, 130, 246, 0.3),
    0 0 20px rgba(59, 130, 246, 0.2);
}

.liquid-delete-button {
  background: rgba(239, 68, 68, 0.2);
  border-color: rgba(239, 68, 68, 0.4);
  color: #fca5a5;
}

.liquid-delete-button:hover {
  background: rgba(239, 68, 68, 0.3);
  border-color: rgba(239, 68, 68, 0.6);
  box-shadow:
    0 4px 12px rgba(239, 68, 68, 0.3),
    0 0 20px rgba(239, 68, 68, 0.2);
}

/* ===== Liquid Glass Modal ===== */

.liquid-modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: var(--glass-blur);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
  padding: 1rem;
  animation: liquid-modal-backdrop-appear var(--liquid-duration-normal) ease-out;
}

@keyframes liquid-modal-backdrop-appear {
  from {
    opacity: 0;
    backdrop-filter: blur(0px);
  }
  to {
    opacity: 1;
    backdrop-filter: var(--glass-blur);
  }
}

.liquid-create-modal {
  width: 100%;
  max-width: 500px;
  padding: 2rem;
  backdrop-filter: var(--glass-blur-strong) var(--glass-saturation);
  animation: liquid-modal-appear var(--liquid-duration-normal) cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes liquid-modal-appear {
  from {
    opacity: 0;
    transform: translateY(-30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.liquid-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.liquid-modal-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0;
  background: linear-gradient(90deg, var(--accent-cyan), var(--accent-blue));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.liquid-close-button {
  padding: 0.5rem;
  min-width: 40px;
  min-height: 40px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.1);
  color: var(--text-secondary);
}

.liquid-close-button:hover {
  background: rgba(239, 68, 68, 0.2);
  border-color: rgba(239, 68, 68, 0.4);
  color: #fca5a5;
}

.liquid-modal-body {
  margin-bottom: 2rem;
}

.liquid-input-group {
  margin-bottom: 1.5rem;
}

.liquid-input-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  margin-bottom: 0.5rem;
  color: var(--text-primary);
}

.liquid-text-input,
.liquid-textarea-input {
  width: 100%;
  padding: 0.875rem 1rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: 12px;
  color: var(--text-primary);
  font-size: 0.875rem;
  transition: all var(--liquid-duration-normal) ease;
  backdrop-filter: blur(8px);
  box-sizing: border-box;
}

.liquid-text-input:focus,
.liquid-textarea-input:focus {
  outline: none;
  border-color: var(--accent-blue);
  background: rgba(255, 255, 255, 0.08);
  box-shadow:
    0 0 0 2px rgba(59, 130, 246, 0.25),
    0 4px 12px rgba(59, 130, 246, 0.15);
}

.liquid-textarea-input {
  resize: vertical;
  min-height: 80px;
}

.liquid-modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
}

.liquid-cancel-button {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.15);
  color: var(--text-secondary);
  padding: 0.75rem 1.5rem;
}

.liquid-cancel-button:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(255, 255, 255, 0.25);
  color: var(--text-primary);
}

.liquid-submit-button {
  background: linear-gradient(135deg, var(--accent-cyan), var(--accent-blue));
  border: none;
  color: white;
  font-weight: 600;
  padding: 0.75rem 1.5rem;
}

.liquid-submit-button:hover:not(:disabled) {
  transform: translateY(-2px) scale(1.02);
  box-shadow:
    0 8px 24px rgba(0, 0, 0, 0.2),
    0 4px 12px var(--accent-cyan-glow),
    0 0 20px var(--accent-blue-glow);
}

.liquid-submit-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background: rgba(75, 85, 99, 0.3);
  color: var(--text-secondary);
  transform: none;
  box-shadow: none;
}

.liquid-btn-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: liquid-spin 1s linear infinite;
  margin-right: 0.5rem;
}

/* ===== Creative Visual Elements & Enhancements ===== */

/* Organic cursor interactions */
.liquid-glass-button,
.liquid-glass-panel,
.liquid-project-card {
  cursor: pointer;
  transition: all var(--liquid-duration-normal) cubic-bezier(0.23, 1, 0.32, 1);
}

/* Enhanced liquid distortion effects */
.liquid-glass-panel::before,
.liquid-glass-button::before {
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(0, 212, 255, 0.05) 25%,
    rgba(59, 130, 246, 0.05) 50%,
    rgba(255, 255, 255, 0.05) 75%,
    rgba(255, 255, 255, 0.02) 100%);
}

/* Subtle glow accents */
.liquid-glass-button:hover,
.liquid-glass-panel:hover {
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.2),
    0 4px 16px var(--accent-blue-glow),
    0 0 24px var(--accent-cyan-glow),
    inset 0 1px 0 rgba(255, 255, 255, 0.15);
}

/* Animated background patterns */
.ai-analyzer-page::after {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(circle at 20% 20%, var(--accent-cyan-glow) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, var(--accent-blue-glow) 0%, transparent 50%);
  animation: liquid-background-pulse 8s ease-in-out infinite alternate;
  z-index: -1;
  pointer-events: none;
}

@keyframes liquid-background-pulse {
  0% {
    opacity: 0.3;
    transform: scale(1);
  }
  100% {
    opacity: 0.6;
    transform: scale(1.1);
  }
}

/* Floating particle effects */
.liquid-glass-header::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(circle at 25% 25%, rgba(0, 212, 255, 0.1) 2px, transparent 2px),
    radial-gradient(circle at 75% 75%, rgba(59, 130, 246, 0.1) 2px, transparent 2px);
  background-size: 60px 60px;
  background-position: 0 0, 30px 30px;
  animation: liquid-particles 20s linear infinite;
  pointer-events: none;
  z-index: -1;
}

@keyframes liquid-particles {
  0% {
    transform: translateY(0);
  }
  100% {
    transform: translateY(-60px);
  }
}

/* ===== Responsive Design - Liquid Glass Optimizations ===== */

/* Large screens (1400px+) */
@media (min-width: 1400px) {
  .floating-nav-container {
    gap: 3rem;
    padding: 0 1rem;
  }

  .liquid-title-panel {
    min-width: 400px;
    padding: 1.25rem 2.5rem;
  }

  .liquid-title {
    font-size: 1.75rem;
  }

  .liquid-projects-grid {
    grid-template-columns: repeat(auto-fill, minmax(450px, 1fr));
    gap: 2rem;
  }
}

/* Medium screens (768px - 1024px) */
@media (max-width: 1024px) {
  .liquid-main-container {
    padding: 7rem 1.5rem 1.5rem;
  }

  /* Enhanced control section for medium screens */
  .liquid-control-section {
    gap: 1.25rem;
    margin-bottom: 2.5rem;
  }

  .control-header-panel {
    padding: 1.75rem 2rem;
  }

  .control-header-content {
    flex-direction: column;
    align-items: stretch;
    gap: 1.5rem;
    text-align: center;
  }

  .section-title {
    font-size: 1.5rem;
  }

  .primary-create-button {
    min-width: 220px;
    align-self: center;
  }

  .view-control-panel {
    padding: 1.25rem 1.75rem;
  }

  .view-switcher-enhanced {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .switcher-options-container {
    justify-content: center;
  }

  .liquid-projects-grid {
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 1.25rem;
  }

  .liquid-project-card {
    padding: 1.5rem;
  }
}

/* Tablet screens (768px) */
@media (max-width: 768px) {
  .liquid-glass-header {
    padding: 1rem 1.5rem;
  }

  .floating-nav-container {
    grid-template-columns: auto 1fr auto;
    gap: 1rem;
  }

  .liquid-title-panel {
    min-width: 250px;
    padding: 0.875rem 1.5rem;
  }

  .liquid-title {
    font-size: 1.25rem;
  }

  .liquid-subtitle {
    font-size: 0.8125rem;
  }

  .header-controls {
    gap: 0.75rem;
  }

  .liquid-user-badge {
    min-width: 140px;
  }

  .liquid-user-badge .user-name {
    max-width: 100px;
    font-size: 0.8125rem;
  }

  .liquid-main-container {
    padding: 6rem 1rem 1rem;
  }

  /* Enhanced control section for tablet */
  .liquid-control-section {
    gap: 1rem;
    margin-bottom: 2rem;
  }

  .control-header-panel {
    padding: 1.5rem 1.75rem;
  }

  .section-title {
    font-size: 1.375rem;
  }

  .section-subtitle {
    font-size: 0.9375rem;
  }

  .primary-create-button {
    padding: 14px 28px;
    min-width: 200px;
    font-size: 0.875rem;
  }

  .view-control-panel {
    padding: 1rem 1.5rem;
  }

  .liquid-switcher-option.enhanced {
    min-width: 140px;
    padding: 0.875rem 1.25rem;
  }

  .liquid-switcher-indicator.enhanced.all {
    width: 140px;
  }

  .liquid-switcher-indicator.enhanced.favorites {
    left: calc(140px + 0.75rem);
    width: 140px;
  }

  .liquid-switcher-option {
    min-width: 120px;
    padding: 0.625rem 1rem;
    font-size: 0.8125rem;
  }

  .liquid-switcher-indicator.all {
    width: 120px;
  }

  .liquid-switcher-indicator.favorites {
    left: calc(120px + 0.5rem);
    width: 120px;
  }

  .liquid-projects-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .liquid-project-card {
    padding: 1.25rem;
  }

  .liquid-card-actions {
    gap: 0.5rem;
  }

  .liquid-action-button {
    padding: 0.625rem 0.875rem;
    font-size: 0.8125rem;
  }
}

/* Mobile screens (480px) */
@media (max-width: 480px) {
  .liquid-glass-header {
    padding: 0.875rem 1rem;
  }

  .floating-nav-container {
    grid-template-columns: 1fr;
    grid-template-areas:
      "title"
      "back"
      "controls";
    gap: 0.75rem;
    text-align: center;
  }

  .liquid-back-button {
    grid-area: back;
    justify-self: start;
    min-width: 120px;
    padding: 0.75rem 1rem;
    font-size: 0.8125rem;
  }

  .liquid-title-panel {
    grid-area: title;
    justify-self: center;
    min-width: auto;
    width: 100%;
    max-width: 300px;
    padding: 0.75rem 1.25rem;
  }

  .liquid-title {
    font-size: 1.125rem;
  }

  .liquid-subtitle {
    font-size: 0.75rem;
  }

  .header-controls {
    grid-area: controls;
    justify-self: center;
    flex-direction: row;
    justify-content: center;
    gap: 0.5rem;
  }

  .liquid-user-badge {
    min-width: 120px;
    padding: 0.625rem 1rem;
    font-size: 0.8125rem;
  }

  .liquid-user-badge .user-name {
    max-width: 80px;
    font-size: 0.75rem;
  }

  .liquid-lang-button {
    min-width: 80px;
    padding: 0.625rem 1rem;
    font-size: 0.8125rem;
  }

  .liquid-main-container {
    padding: 12rem 0.75rem 0.75rem;
  }

  .liquid-welcome-card {
    padding: 2rem 1.5rem;
  }

  .liquid-welcome-title {
    font-size: 1.5rem;
  }

  .liquid-welcome-desc {
    font-size: 1rem;
  }

  /* Enhanced mobile control section */
  .liquid-control-section {
    gap: 0.875rem;
    margin-bottom: 1.5rem;
  }

  .control-header-panel {
    padding: 1.25rem 1.5rem;
  }

  .section-title {
    font-size: 1.25rem;
  }

  .section-subtitle {
    font-size: 0.875rem;
  }

  .primary-create-button {
    min-width: 180px;
    padding: 12px 24px;
    font-size: 0.8125rem;
  }

  .view-control-panel {
    padding: 0.875rem 1.25rem;
  }

  .view-switcher-enhanced {
    flex-direction: column;
    gap: 0.75rem;
  }

  .switcher-options-container {
    flex-direction: column;
    gap: 0.5rem;
  }

  .liquid-switcher-option.enhanced {
    min-width: auto;
    width: 100%;
    padding: 0.875rem 1.25rem;
    justify-content: center;
  }

  .liquid-switcher-indicator.enhanced {
    display: none; /* Hide indicator on mobile for simplicity */
  }

  .liquid-projects-grid {
    gap: 0.875rem;
  }

  .liquid-project-card {
    padding: 1rem;
  }

  .liquid-card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }

  .liquid-star-button {
    align-self: flex-end;
    min-width: 36px;
    min-height: 36px;
    padding: 0.375rem;
  }

  .liquid-card-actions {
    flex-direction: column;
    gap: 0.5rem;
  }

  .liquid-action-button {
    padding: 0.75rem;
    font-size: 0.8125rem;
  }

  .liquid-create-modal {
    padding: 1.5rem;
    margin: 0.5rem;
  }

  .liquid-modal-title {
    font-size: 1.25rem;
  }

  .liquid-modal-footer {
    flex-direction: column;
    gap: 0.75rem;
  }

  .liquid-cancel-button,
  .liquid-submit-button {
    width: 100%;
    justify-content: center;
  }
}

/* Extra small screens (320px) */
@media (max-width: 320px) {
  .liquid-glass-header {
    padding: 0.75rem;
  }

  .liquid-main-container {
    padding: 11rem 0.5rem 0.5rem;
  }

  .liquid-welcome-card {
    padding: 1.5rem 1rem;
  }

  .liquid-welcome-title {
    font-size: 1.25rem;
  }

  .liquid-title {
    font-size: 1rem;
  }

  .liquid-project-card {
    padding: 0.875rem;
  }

  .liquid-create-modal {
    padding: 1.25rem;
    margin: 0.25rem;
  }
}

/* Background effects - Enhanced */
.ai-background {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: -1;
  overflow: hidden;
  pointer-events: none;
}

/* Enhanced background grid pattern for liquid glass aesthetic */
.grid-pattern {
  position: absolute;
  width: 200%;
  height: 200%;
  top: -50%;
  left: -50%;
  background-image:
    linear-gradient(rgba(0, 212, 255, 0.05) 1px, transparent 1px),
    linear-gradient(90deg, rgba(59, 130, 246, 0.05) 1px, transparent 1px);
  background-size: 40px 40px;
  transform: perspective(600px) rotateX(60deg);
  animation: liquid-grid-movement 80s linear infinite;
  opacity: 0.3;
}

.floating-particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    radial-gradient(rgba(0, 212, 255, 0.15) 2px, transparent 2px),
    radial-gradient(rgba(59, 130, 246, 0.15) 2px, transparent 2px);
  background-size: 80px 80px;
  background-position: 0 0, 40px 40px;
  animation: liquid-particle-movement 100s linear infinite;
  opacity: 0.2;
}

/* 电路图背景 - 增强版 */
.circuit-pattern {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0.3; /* 增加不透明度 */
  overflow: hidden;
  pointer-events: none;
}

.circuit-pattern::before,
.circuit-pattern::after {
  content: '';
  position: absolute;
  background-repeat: no-repeat;
  background-size: contain;
  animation: circuit-pulse 4s ease-in-out infinite alternate;
}

.circuit-pattern::before {
  top: 5%;
  right: 5%;
  width: 600px;
  height: 600px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 800 800' width='800' height='800'%3E%3Cpath fill='none' stroke='%233b82f6' stroke-width='3' d='M200,100 L400,100 L400,200 M400,100 L600,100 L600,300 M600,350 L600,500 L450,500 M400,500 L250,500 L250,650 M250,700 L250,750 L650,750 M400,750 L400,500 M450,100 L450,300 L600,300 M50,300 L200,300 L200,100 M50,300 L50,700 L250,700 M650,750 L650,250 L450,250 L450,300 M50,500 L200,500 L200,600 M400,300 C250,300 250,450 400,450 C550,450 550,300 400,300 M400,250 L400,300 M400,450 L400,500'/%3E%3C/svg%3E");
  transform: rotate(5deg);
  filter: drop-shadow(0 0 20px rgba(59, 130, 246, 0.6)); /* 增强阴影 */
}

.circuit-pattern::after {
  bottom: 10%;
  left: 5%;
  width: 500px;
  height: 500px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 800 800' width='800' height='800'%3E%3Cpath fill='none' stroke='%2334d399' stroke-width='3' d='M400,50 L400,150 M400,200 L400,300 M400,200 C250,200 250,500 400,500 M400,500 C550,500 550,200 400,200 M400,500 L400,600 M400,650 L400,750 M300,150 L500,150 M300,750 L500,750 M100,300 L300,300 M100,600 L300,600 M500,300 L700,300 M500,600 L700,600 M100,300 C50,300 50,600 100,600 M700,300 C750,300 750,600 700,600 M250,400 L350,400 M450,400 L550,400 M400,350 L400,450'/%3E%3C/svg%3E");
  transform: rotate(-10deg);
  filter: drop-shadow(0 0 20px rgba(52, 211, 153, 0.6)); /* 增强阴影 */
  animation-delay: 2s;
}

/* 添加全屏电路背景 */
.ai-analyzer-page::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='100%25' height='100%25'%3E%3Cdefs%3E%3Cpattern id='circuit' patternUnits='userSpaceOnUse' width='500' height='500' patternTransform='scale(0.75) rotate(0)'%3E%3Cpath fill='none' stroke='%233b82f680' stroke-width='1' d='M100,100 L100,400 L400,400 M250,100 L250,250 L400,250 M400,100 L400,400 M100,250 L175,250 M325,250 L400,250 M250,250 C250,200 300,200 300,250 C300,300 250,300 250,250 M0,0 L0,500 M500,0 L500,500 M0,0 L500,0 M0,500 L500,500'/%3E%3C/pattern%3E%3C/defs%3E%3Crect width='100%25' height='100%25' fill='url(%23circuit)' opacity='0.12'/%3E%3C/svg%3E"); /* 增加不透明度 */
  z-index: -2; /* 确保在背景层后面 */
  opacity: 1;
  pointer-events: none;
}

/* 新增发光效果 */
.glow-effect {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at 50% 50%, rgba(59, 130, 246, 0.15), transparent 70%);
  pointer-events: none;
  opacity: 0.8;
  animation: glow-pulse 8s ease-in-out infinite alternate;
}

@keyframes glow-pulse {
  0% {
    opacity: 0.5;
    transform: scale(1);
  }
  100% {
    opacity: 0.8;
    transform: scale(1.2);
  }
}

@keyframes circuit-pulse {
  0% {
    opacity: 0.2;
    filter: drop-shadow(0 0 5px rgba(59, 130, 246, 0.3));
  }
  100% {
    opacity: 0.5; /* 增加最大不透明度 */
    filter: drop-shadow(0 0 25px rgba(59, 130, 246, 0.7)); /* 增强阴影 */
  }
}

@keyframes liquid-grid-movement {
  0% {
    transform: perspective(600px) rotateX(60deg) translateY(0);
  }
  100% {
    transform: perspective(600px) rotateX(60deg) translateY(40px);
  }
}

@keyframes liquid-particle-movement {
  0% {
    transform: translateY(0);
  }
  100% {
    transform: translateY(-120px);
  }
}

/* 悬浮导航按钮 - 修复重叠问题 */
.floating-nav-buttons {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 50;
  padding: 1.5rem 2rem;
  display: grid;
  grid-template-columns: minmax(140px, 1fr) auto minmax(280px, 1fr);
  grid-template-areas: "left center right";
  align-items: flex-start;
  gap: 1.5rem;
  pointer-events: none; /* 允许点击穿透到背景 */
  min-height: 80px; /* 确保足够的高度避免重叠 */
  box-sizing: border-box;
}

.floating-nav-buttons > * {
  pointer-events: auto; /* 恢复子元素的点击事件 */
}

/* 基础悬浮按钮样式 */
.floating-btn {
  background: rgba(22, 31, 49, 0.85);
  backdrop-filter: blur(12px);
  border: 1px solid rgba(59, 130, 246, 0.3);
  border-radius: 16px;
  color: #e5e7eb;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow:
    0 4px 12px rgba(0, 0, 0, 0.15),
    0 2px 6px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.1),
    inset 0 -1px 0 rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.25rem;
}

/* 添加液体玻璃质感的额外层 */
.floating-btn::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0.05) 50%,
    rgba(255, 255, 255, 0.02) 100%);
  border-radius: inherit;
  pointer-events: none;
  z-index: 1;
}

.floating-btn::before {
  /* 移除动画效果，避免UI问题 */
  display: none;
}

/* 确保按钮内容在正确的层级 */
.floating-btn > * {
  position: relative;
  z-index: 3;
}

.floating-btn:hover {
  transform: translateY(-2px) scale(1.02);
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.2);
  box-shadow:
    0 8px 24px rgba(59, 130, 246, 0.15),
    0 4px 12px rgba(59, 130, 246, 0.08),
    0 0 20px rgba(59, 130, 246, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.12);

  /* 简化发光效果 */
  filter: brightness(1.05);
}

/* 移除动画效果 */

.floating-btn:active {
  transform: translateY(-1px);
}

/* 返回按钮 - 左侧区域 */
.back-floating-btn {
  grid-area: left;
  justify-self: start;
  min-width: 140px;
  max-width: 200px;
}

.back-floating-btn .btn-icon {
  font-size: 1.125rem;
  font-weight: 600;
}

.back-floating-btn:hover {
  transform: translateY(-2px) scale(1.02);
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(52, 211, 153, 0.4);
  box-shadow:
    0 8px 24px rgba(52, 211, 153, 0.2),
    0 4px 12px rgba(52, 211, 153, 0.12),
    0 0 20px rgba(52, 211, 153, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.12);

  /* 简化发光效果 */
  filter: brightness(1.08);
}

/* 悬浮标题 - 中心区域 */
.floating-title {
  grid-area: center;
  justify-self: center;
  align-self: start;
  padding: 1rem 2rem;
  background: rgba(22, 31, 49, 0.85);
  backdrop-filter: blur(12px);
  border: 1px solid rgba(59, 130, 246, 0.3);
  border-radius: 20px;
  box-shadow:
    0 6px 16px rgba(0, 0, 0, 0.15),
    0 3px 8px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  pointer-events: none;
  max-width: 300px;
  min-width: 200px;
  white-space: nowrap;
  text-align: center;
}

.floating-title .page-title {
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0;
  background: linear-gradient(90deg, #60a5fa, #34d399);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
  text-shadow: 0 0 30px rgba(59, 130, 246, 0.3);
  white-space: nowrap;
}

/* 右侧按钮组 - 右侧区域 */
.floating-right-group {
  grid-area: right;
  justify-self: end;
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  max-width: 400px;
  min-width: 280px; /* 确保最小宽度 */
  overflow: hidden; /* 防止溢出 */
}

/* 用户欢迎信息悬浮按钮 */
.welcome-floating-btn {
  /* 继承floating-btn的基础样式 */
  background: rgba(22, 31, 49, 0.85);
  backdrop-filter: blur(16px) saturate(180%);
  -webkit-backdrop-filter: blur(16px) saturate(180%);
  border: 1px solid rgba(52, 211, 153, 0.3);
  border-radius: 16px;
  color: #e5e7eb;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  padding: 0.875rem 1.25rem;

  /* 特定样式 */
  min-width: 160px;
  max-width: 220px;
  justify-content: flex-start;
  flex-shrink: 1; /* 允许收缩 */
  display: flex;
  align-items: center;
  gap: 0.75rem;

  /* 简化过渡效果 */
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  /* 基础阴影 */
  box-shadow:
    0 4px 12px rgba(0, 0, 0, 0.1),
    0 0 16px rgba(52, 211, 153, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.08);
}

/* 隐藏状态的欢迎按钮 */
.welcome-floating-btn:disabled {
  pointer-events: none;
  cursor: default;
}

.welcome-floating-btn[style*="visibility: hidden"] {
  pointer-events: none;
}

.welcome-floating-btn .welcome-icon {
  font-size: 1rem;
}

.welcome-floating-btn .welcome-text {
  font-size: 0.8125rem;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 120px;
  flex-shrink: 1;
}

.welcome-floating-btn:hover {
  transform: translateY(-2px) scale(1.02);
  background: rgba(22, 31, 49, 0.9);
  border-color: rgba(52, 211, 153, 0.4);
  box-shadow:
    0 6px 16px rgba(0, 0, 0, 0.15),
    0 0 16px rgba(52, 211, 153, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  filter: brightness(1.05);
}

/* 悬浮语言切换器 */
.floating-language-switcher {
  position: relative;
  z-index: 60;
  flex-shrink: 0; /* 防止被压缩 */
}

.language-dropdown {
  position: relative;
}

.language-floating-btn {
  /* 继承floating-btn的基础样式 */
  background: rgba(22, 31, 49, 0.85);
  backdrop-filter: blur(16px) saturate(180%);
  -webkit-backdrop-filter: blur(16px) saturate(180%);
  border: 1px solid rgba(147, 51, 234, 0.3);
  border-radius: 16px;
  color: #e5e7eb;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  padding: 0.875rem 1.25rem;

  /* 特定样式 */
  min-width: 100px;
  max-width: 120px;
  justify-content: space-between;
  flex-shrink: 0; /* 防止语言按钮被压缩 */
  display: flex;
  align-items: center;
  gap: 0.5rem;

  /* 简化过渡效果 */
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  /* 基础阴影 */
  box-shadow:
    0 4px 12px rgba(0, 0, 0, 0.1),
    0 0 16px rgba(147, 51, 234, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.08);
}

.language-floating-btn .language-flag {
  font-size: 1rem;
}

.language-floating-btn .language-text {
  font-size: 0.8125rem;
  font-weight: 500;
}

.language-floating-btn .dropdown-arrow {
  font-size: 0.75rem;
  transition: transform 0.3s ease;
  color: #9ca3af;
  margin-left: 0.25rem;
}

.language-floating-btn .dropdown-arrow.open {
  transform: rotate(180deg);
}

.language-floating-btn:hover {
  transform: translateY(-2px) scale(1.02);
  background: rgba(22, 31, 49, 0.9);
  border-color: rgba(147, 51, 234, 0.4);
  box-shadow:
    0 6px 16px rgba(0, 0, 0, 0.15),
    0 0 16px rgba(147, 51, 234, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  filter: brightness(1.05);
}

/* 悬浮语言菜单 */
.floating-language-menu {
  position: absolute;
  top: calc(100% + 0.75rem);
  right: 0;
  background: rgba(22, 31, 49, 0.95);
  backdrop-filter: blur(12px);
  border: 1px solid rgba(59, 130, 246, 0.3);
  border-radius: 12px;
  box-shadow:
    0 12px 28px rgba(0, 0, 0, 0.25),
    0 6px 14px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  overflow: hidden;
  z-index: 100;
  min-width: 140px;
  animation: floating-dropdown-appear 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes floating-dropdown-appear {
  from {
    opacity: 0;
    transform: translateY(-10px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.floating-language-menu .language-option {
  width: 100%;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.875rem 1.125rem;
  background: none;
  border: none;
  color: #e5e7eb;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: left;
  position: relative;
}

.floating-language-menu .language-option::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background: transparent;
  transition: all 0.2s ease;
}

.floating-language-menu .language-option:hover {
  background: rgba(59, 130, 246, 0.1);
  color: #60a5fa;
}

.floating-language-menu .language-option:hover::before {
  background: #60a5fa;
}

.floating-language-menu .language-option.active {
  background: rgba(37, 99, 235, 0.15);
  color: #60a5fa;
}

.floating-language-menu .language-option.active::before {
  background: #60a5fa;
}

.floating-language-menu .option-flag {
  font-size: 1rem;
}

.floating-language-menu .option-text {
  flex: 1;
  font-weight: 500;
}

/* 主内容 - 全新简洁设计 */
.main-content {
  padding: 6rem 2rem 2rem;
  min-height: 100vh;
  position: relative;
  z-index: 1;
}

.content-container {
  max-width: 1200px;
  margin: 0 auto;
}

/* 操作栏 - 液态玻璃设计 */
.action-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2.5rem;
  padding: 0;
  gap: 1.5rem;

  /* 简洁的设计 */
  background: transparent;
  border: none;
  position: relative;
}

/* 移除复杂的背景效果 */

.action-bar > * {
  position: relative;
  z-index: 2;
}

/* ===== Enhanced Create Button - Refined Liquid Glass Design ===== */
.create-btn {
  background: rgba(255, 255, 255, 0.06);
  backdrop-filter: blur(16px) saturate(150%);
  -webkit-backdrop-filter: blur(16px) saturate(150%);
  border: 1px solid rgba(59, 130, 246, 0.3);
  border-radius: 12px;
  color: #ffffff;
  font-size: 14px;
  font-weight: 500;
  padding: 10px 20px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  z-index: 1;

  /* 精简的玻璃效果 */
  box-shadow:
    0 4px 20px rgba(0, 0, 0, 0.2),
    0 2px 8px rgba(59, 130, 246, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.15);

  /* 简化的液体扭曲效果 */
  background-image:
    radial-gradient(circle at 25% 25%, rgba(59, 130, 246, 0.1) 0%, transparent 40%);

  /* 柔和的发光效果 */
  text-shadow: 0 0 8px rgba(59, 130, 246, 0.2);

  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.create-btn::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0.05) 50%,
    rgba(255, 255, 255, 0.02) 100%);
  border-radius: inherit;
  pointer-events: none;
  z-index: 1;
}

.create-btn::before {
  /* 移除动画效果，避免UI问题 */
  display: none;
}

.create-btn > * {
  position: relative;
  z-index: 3;
}

.create-btn:hover:not(.disabled) {
  transform: translateY(-2px) scale(1.02);
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(59, 130, 246, 0.4);

  /* 简化hover发光效果 */
  box-shadow:
    0 6px 20px rgba(59, 130, 246, 0.2),
    0 0 20px rgba(59, 130, 246, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.15);

  /* 简化发光效果 */
  filter: brightness(1.05);
  text-shadow: 0 0 12px rgba(59, 130, 246, 0.4);
}

/* 移除hover动画效果 */

.create-btn.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background: rgba(75, 85, 99, 0.3) !important;
  color: #9ca3af !important;
  box-shadow: none !important;
  transform: none !important;
}

/* 过滤标签 - 液态玻璃风格 */
.filter-tabs {
  display: flex;
  gap: 0.75rem;
}

.tab-btn {
  background: rgba(22, 31, 49, 0.7);
  backdrop-filter: blur(8px);
  border: 1px solid rgba(75, 85, 99, 0.3);
  border-radius: 10px;
  color: #9ca3af;
  padding: 0.625rem 1.25rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow:
    0 2px 8px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.05);
  position: relative;
  overflow: hidden;
}

.tab-btn::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.05) 0%,
    rgba(255, 255, 255, 0.02) 100%);
  border-radius: inherit;
  pointer-events: none;
}

.tab-btn:hover {
  transform: translateY(-1px);
  background: rgba(22, 31, 49, 0.8);
  color: #e5e7eb;
  border-color: rgba(75, 85, 99, 0.5);
  box-shadow:
    0 4px 12px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.tab-btn.active {
  background: rgba(59, 130, 246, 0.2);
  color: #60a5fa;
  border-color: rgba(59, 130, 246, 0.5);
  box-shadow:
    0 4px 12px rgba(59, 130, 246, 0.2),
    0 0 20px rgba(59, 130, 246, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

/* 错误信息 - 液态玻璃风格 */
.error-message {
  background: rgba(22, 31, 49, 0.85);
  backdrop-filter: blur(12px);
  border: 1px solid rgba(239, 68, 68, 0.4);
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #fca5a5;
  box-shadow:
    0 8px 32px rgba(239, 68, 68, 0.1),
    0 4px 16px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  position: relative;
  overflow: hidden;
}

.error-message::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.05) 0%,
    rgba(255, 255, 255, 0.02) 100%);
  border-radius: inherit;
  pointer-events: none;
}

.error-message > * {
  position: relative;
  z-index: 1;
}

.retry-btn {
  background: rgba(22, 31, 49, 0.7);
  backdrop-filter: blur(8px);
  border: 1px solid rgba(239, 68, 68, 0.4);
  color: #fca5a5;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow:
    0 2px 8px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.05);
  position: relative;
  overflow: hidden;
}

.retry-btn::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.05) 0%,
    rgba(255, 255, 255, 0.02) 100%);
  border-radius: inherit;
  pointer-events: none;
}

.retry-btn:hover {
  transform: translateY(-2px);
  background: rgba(239, 68, 68, 0.2);
  border-color: rgba(239, 68, 68, 0.6);
  box-shadow:
    0 4px 12px rgba(239, 68, 68, 0.3),
    0 0 20px rgba(239, 68, 68, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

/* 加载状态 - 液态玻璃风格 */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  text-align: center;
  background: rgba(22, 31, 49, 0.85);
  backdrop-filter: blur(12px);
  border: 1px solid rgba(59, 130, 246, 0.3);
  border-radius: 16px;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.3),
    0 4px 16px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  position: relative;
  overflow: hidden;
}

.loading-state::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0.05) 50%,
    rgba(255, 255, 255, 0.02) 100%);
  border-radius: inherit;
  pointer-events: none;
}

.loading-state > * {
  position: relative;
  z-index: 1;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(59, 130, 246, 0.3);
  border-top: 3px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1.5rem;
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-state p {
  color: #9ca3af;
  margin: 0;
  font-size: 0.875rem;
}

/* 项目内容区 - 优化设计 */
.projects-section {
  min-height: 400px;
  position: relative;

  /* 添加微妙的背景纹理 */
  background-image:
    radial-gradient(circle at 20% 20%, rgba(59, 130, 246, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(16, 185, 129, 0.02) 0%, transparent 50%);
}

/* 空状态 - 液态玻璃风格 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  text-align: center;
  background: rgba(22, 31, 49, 0.85);
  backdrop-filter: blur(12px);
  border: 1px solid rgba(59, 130, 246, 0.3);
  border-radius: 16px;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.3),
    0 4px 16px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  position: relative;
  overflow: hidden;
}

.empty-state::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0.05) 50%,
    rgba(255, 255, 255, 0.02) 100%);
  border-radius: inherit;
  pointer-events: none;
}

.empty-state > * {
  position: relative;
  z-index: 1;
}

.empty-icon {
  font-size: 4rem;
  margin-bottom: 1.5rem;
  opacity: 0.8;
  animation: float 3s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-10px); }
}

.empty-state h3 {
  color: #e5e7eb;
  margin: 0 0 1rem;
  font-size: 1.5rem;
  font-weight: 600;
  background: linear-gradient(90deg, #60a5fa, #34d399);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.empty-state p {
  color: #9ca3af;
  margin: 0 0 2.5rem;
  max-width: 450px;
  font-size: 1rem;
  line-height: 1.6;
}

.projects-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 1.5rem;
  margin-top: 0.5rem;
}

.project-item {
  background: rgba(22, 31, 49, 0.85);
  backdrop-filter: blur(12px);
  border: 1px solid rgba(59, 130, 246, 0.3);
  border-radius: 16px;
  padding: 1.75rem;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  /* 简化阴影系统 */
  box-shadow:
    0 8px 24px rgba(0, 0, 0, 0.12),
    0 4px 12px rgba(0, 0, 0, 0.08),
    0 0 20px rgba(59, 130, 246, 0.06),
    inset 0 1px 0 rgba(255, 255, 255, 0.08);
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  height: 100%;
}

/* 液态玻璃质感层 */
.project-item::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0.05) 50%,
    rgba(255, 255, 255, 0.02) 100%);
  border-radius: inherit;
  pointer-events: none;
  z-index: 1;
}

.project-item > * {
  position: relative;
  z-index: 2;
}

.project-item:hover {
  transform: translateY(-4px) scale(1.02);
  /* 简化hover发光效果 */
  box-shadow:
    0 12px 32px rgba(0, 0, 0, 0.15),
    0 6px 16px rgba(0, 0, 0, 0.1),
    0 0 24px rgba(59, 130, 246, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.12);
  border-color: rgba(59, 130, 246, 0.4);
}

.project-info {
  flex: 1;
  cursor: pointer;
}

.project-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
  gap: 1rem;
}

.project-title {
  color: #e5e7eb;
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  flex: 1;
  line-height: 1.3;
}

.star-button {
  background: rgba(22, 31, 49, 0.7);
  backdrop-filter: blur(8px);
  border: 1px solid rgba(75, 85, 99, 0.3);
  color: #9ca3af;
  font-size: 1.25rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 8px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow:
    0 2px 8px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.05);
  position: relative;
  overflow: hidden;
  min-width: 40px;
  min-height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.star-button::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.05) 0%,
    rgba(255, 255, 255, 0.02) 100%);
  border-radius: inherit;
  pointer-events: none;
}

.star-button:hover {
  transform: translateY(-2px) scale(1.1);
  color: #fbbf24;
  background: rgba(251, 191, 36, 0.2);
  border-color: rgba(251, 191, 36, 0.5);
  box-shadow:
    0 4px 12px rgba(251, 191, 36, 0.3),
    0 0 20px rgba(251, 191, 36, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.star-button.starred {
  color: #fbbf24;
  background: rgba(251, 191, 36, 0.2);
  border-color: rgba(251, 191, 36, 0.5);
  box-shadow:
    0 4px 12px rgba(251, 191, 36, 0.3),
    0 0 20px rgba(251, 191, 36, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  text-shadow: 0 0 10px rgba(251, 191, 36, 0.5);
}

.project-desc {
  color: #9ca3af;
  margin: 0 0 1rem;
  font-size: 0.875rem;
  line-height: 1.5;
  flex: 1;
}

.project-date {
  color: #6b7280;
  font-size: 0.75rem;
  margin-bottom: 1.5rem;
}

.project-actions {
  display: flex;
  gap: 0.75rem;
  margin-top: auto;
  padding-top: 1rem;
  border-top: 1px solid rgba(75, 85, 99, 0.3);
}

.action-button {
  background: rgba(22, 31, 49, 0.7);
  backdrop-filter: blur(8px);
  border: 1px solid rgba(75, 85, 99, 0.3);
  color: #9ca3af;
  padding: 0.625rem 1.25rem;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow:
    0 2px 8px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.05);
  position: relative;
  overflow: hidden;
  flex: 1;
}

.action-button::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.05) 0%,
    rgba(255, 255, 255, 0.02) 100%);
  border-radius: inherit;
  pointer-events: none;
}

.action-button:hover {
  transform: translateY(-2px);
  color: #e5e7eb;
  box-shadow:
    0 4px 12px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.action-button.open:hover {
  background: rgba(59, 130, 246, 0.2);
  border-color: rgba(59, 130, 246, 0.5);
  color: #60a5fa;
  box-shadow:
    0 4px 12px rgba(59, 130, 246, 0.3),
    0 0 20px rgba(59, 130, 246, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.action-button.delete:hover {
  background: rgba(239, 68, 68, 0.2);
  border-color: rgba(239, 68, 68, 0.5);
  color: #fca5a5;
  box-shadow:
    0 4px 12px rgba(239, 68, 68, 0.3),
    0 0 20px rgba(239, 68, 68, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

/* 响应式设计 - 液态玻璃布局 */
@media (max-width: 1024px) {
  .projects-list {
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 1rem;
  }
}

@media (max-width: 768px) {
  .main-content {
    padding: 6rem 1rem 1rem;
  }

  .action-bar {
    flex-direction: column;
    gap: 1.5rem;
    align-items: stretch;
    padding: 1.25rem;
  }

  .filter-tabs {
    justify-content: center;
  }

  .projects-list {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .project-item {
    padding: 1.25rem;
  }

  .project-header {
    margin-bottom: 0.75rem;
  }

  .project-title {
    font-size: 1.125rem;
  }

  .project-actions {
    gap: 0.5rem;
    padding-top: 0.75rem;
  }

  .action-button {
    padding: 0.5rem 1rem;
    font-size: 0.75rem;
  }
}

@media (max-width: 480px) {
  .main-content {
    padding: 12rem 0.75rem 0.75rem;
  }

  .action-bar {
    padding: 1rem;
  }

  .filter-tabs {
    gap: 0.5rem;
  }

  .tab-btn {
    flex: 1;
    padding: 0.5rem 0.75rem;
    font-size: 0.75rem;
  }

  .project-item {
    padding: 1rem;
  }

  .project-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }

  .star-button {
    align-self: flex-end;
    min-width: 36px;
    min-height: 36px;
    padding: 0.375rem;
  }

  .project-actions {
    flex-direction: column;
    gap: 0.5rem;
  }

  .action-button {
    padding: 0.625rem;
    font-size: 0.75rem;
  }
}

/* 模态框 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
  padding: 1rem;
}

.modal-content {
  background: #1f2937;
  border-radius: 12px;
  padding: 2rem;
  width: 100%;
  max-width: 500px;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  border: 1px solid rgba(75, 85, 99, 0.5);
  position: relative;
  overflow: hidden;
}

.modal-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, transparent, #3b82f6, #34d399, transparent);
}

.modal-content h2 {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0 0 1.5rem;
  color: #e5e7eb;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  margin-bottom: 0.5rem;
  color: #e5e7eb;
}

input[type="text"],
textarea {
  width: 100%;
  padding: 0.75rem;
  background: #374151;
  border: 1px solid rgba(75, 85, 99, 0.5);
  border-radius: 8px;
  color: #e5e7eb;
  font-size: 0.875rem;
  transition: all 0.2s ease;
}

input[type="text"]:focus,
textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.25);
}

.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 2rem;
}

.btn-secondary {
  background: rgba(55, 65, 81, 0.5);
  color: #e5e7eb;
  border: 1px solid rgba(75, 85, 99, 0.5);
  border-radius: 8px;
  padding: 0.625rem 1.25rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-secondary:hover {
  background: rgba(55, 65, 81, 0.7);
}

.btn-primary {
  background: linear-gradient(135deg, rgba(37, 99, 235, 0.8), rgba(79, 70, 229, 0.8));
  color: white;
  border: none;
  border-radius: 8px;
  padding: 0.625rem 1.25rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1), 0 1px 3px rgba(0, 0, 0, 0.1);
}

.btn-primary:hover:not(:disabled) {
  background: linear-gradient(135deg, rgba(37, 99, 235, 0.9), rgba(79, 70, 229, 0.9));
  transform: translateY(-1px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15), 0 2px 4px rgba(0, 0, 0, 0.1);
}

.btn-primary:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 大屏幕优化 */
@media (min-width: 1400px) {
  .floating-nav-buttons {
    padding: 1.5rem 3rem;
    grid-template-columns: 1fr auto 1fr;
    gap: 2rem;
  }

  .floating-title {
    max-width: 400px;
  }

  .floating-right-group {
    max-width: 500px;
  }

  .welcome-floating-btn {
    min-width: 200px;
    max-width: 280px;
  }

  .welcome-floating-btn .welcome-text {
    max-width: 160px;
  }
}

/* 响应式设计 - 悬浮导航 */
@media (max-width: 768px) {
  .floating-nav-buttons {
    padding: 1rem;
    grid-template-columns: auto 1fr auto;
    grid-template-areas: "left center right";
    gap: 0.75rem;
    min-height: 60px;
  }

  .floating-title {
    max-width: 250px;
    padding: 0.75rem 1.5rem;
  }

  .floating-title .page-title {
    font-size: 1.25rem;
  }

  .back-floating-btn {
    min-width: 120px;
    max-width: 140px;
  }

  .floating-right-group {
    gap: 0.75rem;
    max-width: 300px;
  }

  .welcome-floating-btn {
    min-width: 140px;
    max-width: 180px;
  }

  .welcome-floating-btn .welcome-text {
    max-width: 100px;
    font-size: 0.75rem;
  }

  .language-floating-btn {
    min-width: 90px;
    max-width: 100px;
  }
}

@media (max-width: 480px) {
  .floating-nav-buttons {
    padding: 0.75rem;
    grid-template-columns: 1fr;
    grid-template-areas:
      "center"
      "left"
      "right";
    gap: 0.75rem;
    min-height: auto;
    text-align: center;
  }

  .floating-btn {
    padding: 0.625rem 1rem;
    font-size: 0.8125rem;
  }

  .floating-title {
    padding: 0.75rem 1.5rem;
    max-width: none;
    justify-self: center;
  }

  .floating-title .page-title {
    font-size: 1.125rem;
  }

  .back-floating-btn {
    justify-self: start;
    min-width: 100px;
    max-width: 120px;
  }

  .back-floating-btn .btn-text {
    display: none; /* 在小屏幕上只显示图标 */
  }

  .floating-right-group {
    justify-self: center;
    flex-direction: row;
    justify-content: center;
    gap: 0.5rem;
    max-width: none;
  }

  .welcome-floating-btn {
    min-width: 120px;
    max-width: 160px;
    justify-content: center;
  }

  .welcome-floating-btn .welcome-text {
    font-size: 0.75rem;
    max-width: 80px;
  }

  .language-floating-btn {
    min-width: 80px;
    max-width: 90px;
    justify-content: center;
  }

  .language-floating-btn .language-text {
    font-size: 0.75rem;
  }

  .modal-content {
    padding: 1.5rem;
  }
}



/* 登录提示模态框 */
.login-prompt-modal {
  max-width: 400px;
  text-align: center;
}

.login-prompt-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid rgba(75, 85, 99, 0.3);
}

.login-prompt-header h2 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: #e5e7eb;
}

.modal-close-btn {
  background: none;
  border: none;
  color: #9ca3af;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.modal-close-btn:hover {
  color: #e5e7eb;
  background: rgba(75, 85, 99, 0.3);
}

.login-prompt-body {
  margin-bottom: 2rem;
}

.login-prompt-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.8;
}

.login-prompt-message {
  color: #d1d5db;
  font-size: 0.875rem;
  line-height: 1.5;
  margin: 0;
}

.login-prompt-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
}

.login-prompt-actions .btn-secondary,
.login-prompt-actions .btn-primary {
  flex: 1;
  max-width: 120px;
}

/* 禁用状态的按钮样式 */
.create-btn.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background: rgba(75, 85, 99, 0.3) !important;
  color: #9ca3af !important;
  box-shadow: none !important;
  transform: none !important;
}

.create-btn.disabled:hover {
  transform: none !important;
  box-shadow: none !important;
  background: rgba(75, 85, 99, 0.3) !important;
}

/* 响应式设计增强 - 认证相关 */
@media (max-width: 768px) {
  .login-prompt-modal {
    max-width: calc(100vw - 2rem);
  }

  .login-prompt-actions {
    flex-direction: column;
  }

  .login-prompt-actions .btn-secondary,
  .login-prompt-actions .btn-primary {
    max-width: none;
  }
}

/* 调试样式 - 临时使用，帮助可视化布局问题 */
/* 取消注释以下样式来调试布局重叠问题 */
/*
.floating-nav-buttons {
  border: 2px solid red !important;
  background: rgba(255, 0, 0, 0.1) !important;
}

.back-floating-btn {
  border: 2px solid green !important;
  background: rgba(0, 255, 0, 0.1) !important;
}

.floating-title {
  border: 2px solid blue !important;
  background: rgba(0, 0, 255, 0.1) !important;
}

.floating-right-group {
  border: 2px solid orange !important;
  background: rgba(255, 165, 0, 0.1) !important;
}

.welcome-floating-btn {
  border: 2px solid purple !important;
  background: rgba(128, 0, 128, 0.1) !important;
}

.language-floating-btn {
  border: 2px solid yellow !important;
  background: rgba(255, 255, 0, 0.1) !important;
}
*/
