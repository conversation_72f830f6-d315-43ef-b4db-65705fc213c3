import MathNode from "../math/MathNode.js";
import { ShaderNodeObject } from "../tsl/TSLCore.js";

export const bitangentGeometry: ShaderNodeObject<MathNode>;
export const bitangentLocal: ShaderNodeObject<MathNode>;
export const bitangentView: ShaderNodeObject<MathNode>;
export const bitangentWorld: ShaderNodeObject<MathNode>;
export const transformedBitangentView: ShaderNodeObject<MathNode>;
export const transformedBitangentWorld: ShaderNodeObject<MathNode>;
