{"version": 3, "file": "renameLegacyProperties.test.js", "names": ["describe", "it", "renameLegacyProperties", "formatAs", "minTime", "should", "deep", "equal", "unit", "threshold", "week", "threshold_for_week"], "sources": ["../../source/steps/renameLegacyProperties.test.js"], "sourcesContent": ["import renameLegacyProperties from './renameLegacyProperties.js'\r\n\r\ndescribe('steps/renameLegacyProperties', () => {\r\n\tit('should rename legacy properties', () => {\r\n\t\trenameLegacyProperties({\r\n\t\t\tformatAs: 'now',\r\n\t\t\tminTime: 1\r\n\t\t}).should.deep.equal({\r\n\t\t\tunit: 'now',\r\n\t\t\tthreshold: 1\r\n\t\t})\r\n\t})\r\n\r\n\tit('should rename legacy properties (minTime: undefined)', () => {\r\n\t\trenameLegacyProperties({\r\n\t\t\tformatAs: 'now'\r\n\t\t}).should.deep.equal({\r\n\t\t\tunit: 'now'\r\n\t\t})\r\n\t})\r\n\r\n\tit('should rename legacy properties (`minTime` is an object)', () => {\r\n\t\trenameLegacyProperties({\r\n\t\t\tformatAs: 'now',\r\n\t\t\tminTime: {\r\n\t\t\t\tweek: 2,\r\n\t\t\t\tdefault: 1\r\n\t\t\t}\r\n\t\t}).should.deep.equal({\r\n\t\t\tunit: 'now',\r\n\t\t\tthreshold: 1,\r\n\t\t\tthreshold_for_week: 2\r\n\t\t})\r\n\t})\r\n})"], "mappings": ";;AAAA;;;;AAEAA,QAAQ,CAAC,8BAAD,EAAiC,YAAM;EAC9CC,EAAE,CAAC,iCAAD,EAAoC,YAAM;IAC3C,IAAAC,kCAAA,EAAuB;MACtBC,QAAQ,EAAE,KADY;MAEtBC,OAAO,EAAE;IAFa,CAAvB,EAGGC,MAHH,CAGUC,IAHV,CAGeC,KAHf,CAGqB;MACpBC,IAAI,EAAE,KADc;MAEpBC,SAAS,EAAE;IAFS,CAHrB;EAOA,CARC,CAAF;EAUAR,EAAE,CAAC,sDAAD,EAAyD,YAAM;IAChE,IAAAC,kCAAA,EAAuB;MACtBC,QAAQ,EAAE;IADY,CAAvB,EAEGE,MAFH,CAEUC,IAFV,CAEeC,KAFf,CAEqB;MACpBC,IAAI,EAAE;IADc,CAFrB;EAKA,CANC,CAAF;EAQAP,EAAE,CAAC,0DAAD,EAA6D,YAAM;IACpE,IAAAC,kCAAA,EAAuB;MACtBC,QAAQ,EAAE,KADY;MAEtBC,OAAO,EAAE;QACRM,IAAI,EAAE,CADE;QAER,WAAS;MAFD;IAFa,CAAvB,EAMGL,MANH,CAMUC,IANV,CAMeC,KANf,CAMqB;MACpBC,IAAI,EAAE,KADc;MAEpBC,SAAS,EAAE,CAFS;MAGpBE,kBAAkB,EAAE;IAHA,CANrB;EAWA,CAZC,CAAF;AAaA,CAhCO,CAAR"}