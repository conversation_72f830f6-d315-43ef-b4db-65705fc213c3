"use strict";

var _getStepMinTime = _interopRequireDefault(require("./getStepMinTime.js"));

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { "default": obj }; }

describe('getStepMinTime', function () {
  it('should support `step.test()` function (returns true)', function () {
    (0, _getStepMinTime["default"])({
      test: function test() {
        return true;
      }
    }, {
      prevStep: {
        minTime: 1
      }
    }).should.equal(0);
  });
  it('should support `step.test()` function (returns false)', function () {
    (0, _getStepMinTime["default"])({
      test: function test() {
        return false;
      }
    }, {
      prevStep: {
        minTime: 1
      }
    }).should.equal(9007199254740991);
  });
});
//# sourceMappingURL=getStepMinTime.test.js.map