{"version": 3, "file": "getTimeToNextUpdate.js", "names": ["YEAR", "INFINITY", "getTimeToNextUpdate", "date", "step", "prevStep", "nextStep", "now", "future", "round", "timestamp", "getTime", "getTimeToNextUpdateForUnit", "unit", "_getTimeToNextUpdateForUnit", "timeToStepChange", "getTimeToStepChange", "undefined", "timeToNextUpdate", "getRoundFunction", "formatAs", "Math", "min", "getStepChangesAt", "currentOrNextStep", "minTime", "getStepMinTime", "stepChangesAt"], "sources": ["../../source/steps/getTimeToNextUpdate.js"], "sourcesContent": ["import _getTimeToNextUpdateForUnit from './getTimeToNextUpdateForUnit.js'\r\nimport getStepMinTime from './getStepMinTime.js'\r\nimport { getRoundFunction } from '../round.js'\r\n\r\n// A thousand years is practically a metaphor for \"infinity\".\r\nconst YEAR = 365 * 24 * 60 * 60 * 1000\r\nexport const INFINITY = 1000 * YEAR\r\n\r\n/**\r\n * Gets the time to next update for a date and a step.\r\n * @param  {number} date — The date passed to `.format()`, converted to a timestamp.\r\n * @param  {object} step\r\n * @param  {object} [options.previousStep]\r\n * @param  {object} [options.nextStep]\r\n * @param  {number} options.now\r\n * @param  {boolean} options.future\r\n * @param  {string} [options.round] - (undocumented) Rounding mechanism.\r\n * @return {number} [timeToNextUpdate]\r\n */\r\nexport default function getTimeToNextUpdate(date, step, { prevStep, nextStep, now, future, round }) {\r\n\tconst timestamp = date.getTime ? date.getTime() : date\r\n\r\n\tconst getTimeToNextUpdateForUnit = (unit) => _getTimeToNextUpdateForUnit(unit, timestamp, { now, round })\r\n\r\n\t// For future dates, steps move from the last one to the first one,\r\n\t// while for past dates, steps move from the first one to the last one,\r\n\t// due to the fact that time flows in one direction,\r\n\t// and future dates' interval naturally becomes smaller\r\n\t// while past dates' interval naturally grows larger.\r\n\t//\r\n\t// For future dates, it's the transition\r\n\t// from the current step to the previous step,\r\n\t// therefore check the `minTime` of the current step.\r\n\t//\r\n\t// For past dates, it's the transition\r\n\t// from the current step to the next step,\r\n\t// therefore check the `minTime` of the next step.\r\n\t//\r\n\tconst timeToStepChange = getTimeToStepChange(future ? step : nextStep, timestamp, {\r\n\t\tfuture,\r\n\t\tnow,\r\n\t\tround,\r\n\t\tprevStep: future ? prevStep : step,\r\n\t\t// isFirstStep: future && isFirstStep\r\n\t})\r\n\r\n\tif (timeToStepChange === undefined) {\r\n\t\t// Can't reliably determine \"time to next update\"\r\n\t\t// if not all of the steps provide `minTime`.\r\n\t\treturn\r\n\t}\r\n\r\n\tlet timeToNextUpdate\r\n\r\n\tif (step) {\r\n\t\tif (step.getTimeToNextUpdate) {\r\n\t\t\ttimeToNextUpdate = step.getTimeToNextUpdate(timestamp, {\r\n\t\t\t\tgetTimeToNextUpdateForUnit,\r\n\t\t\t\tgetRoundFunction,\r\n\t\t\t\tnow,\r\n\t\t\t\tfuture,\r\n\t\t\t\tround\r\n\t\t\t})\r\n\t\t}\r\n\r\n\t\tif (timeToNextUpdate === undefined) {\r\n\t\t\t// \"unit\" is now called \"formatAs\".\r\n\t\t\tconst unit = step.unit || step.formatAs\r\n\t\t\tif (unit) {\r\n\t\t\t\t// For some units, like \"now\", there's no defined amount of seconds in them.\r\n\t\t\t\t// In such cases, `getTimeToNextUpdateForUnit()` returns `undefined`,\r\n\t\t\t\t// and the next step's `minTime` could be used to calculate the update interval:\r\n\t\t\t\t// it will just assume that the label never changes for this step.\r\n\t\t\t\ttimeToNextUpdate = getTimeToNextUpdateForUnit(unit)\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\tif (timeToNextUpdate === undefined) {\r\n\t\treturn timeToStepChange\r\n\t}\r\n\r\n\treturn Math.min(timeToNextUpdate, timeToStepChange)\r\n}\r\n\r\nexport function getStepChangesAt(currentOrNextStep, timestamp, { now, future, round, prevStep }) {\r\n\t// The first step's `minTime` is `0` by default.\r\n\t// It doesn't \"change\" steps at zero point\r\n\t// but it does change the wording when switching\r\n\t// from \"future\" to \"past\": \"in ...\" -> \"... ago\".\r\n\t// Therefore, the label should be updated at zero-point too.\r\n\tconst minTime = getStepMinTime(currentOrNextStep, { timestamp, now, future, round, prevStep })\r\n\tif (minTime === undefined) {\r\n\t\treturn\r\n\t}\r\n\tif (future) {\r\n\t\t// The step changes to the previous step\r\n\t\t// as soon as `timestamp - now` becomes\r\n\t\t// less than the `minTime` of the current step:\r\n\t\t// `timestamp - now === minTime - 1`\r\n\t\t// => `now === timestamp - minTime + 1`.\r\n\t\treturn timestamp - minTime * 1000 + 1\r\n\t} else {\r\n\t\t// The step changes to the next step\r\n\t\t// as soon as `now - timestamp` becomes\r\n\t\t// equal to `minTime` of the next step:\r\n\t\t// `now - timestamp === minTime`\r\n\t\t// => `now === timestamp + minTime`.\r\n\r\n\t\t// This is a special case when double-update could be skipped.\r\n\t\tif (minTime === 0 && timestamp === now) {\r\n\t\t\treturn INFINITY\r\n\t\t}\r\n\r\n\t\treturn timestamp + minTime * 1000\r\n\t}\r\n}\r\n\r\nexport function getTimeToStepChange(step, timestamp, {\r\n\tnow,\r\n\tfuture,\r\n\tround,\r\n\tprevStep\r\n}) {\r\n\tif (step) {\r\n\t\tconst stepChangesAt = getStepChangesAt(step, timestamp, {\r\n\t\t\tnow,\r\n\t\t\tfuture,\r\n\t\t\tround,\r\n\t\t\tprevStep\r\n\t\t})\r\n\t\tif (stepChangesAt === undefined) {\r\n\t\t\treturn\r\n\t\t}\r\n\t\treturn stepChangesAt - now\r\n\t} else {\r\n\t\tif (future) {\r\n\t\t\t// No step.\r\n\t\t\t// Update right after zero point, when it changes from \"future\" to \"past\".\r\n\t\t\treturn timestamp - now + 1\r\n\t\t} else {\r\n\t\t\t// The last step doesn't ever change when `date` is in the past.\r\n\t\t\treturn INFINITY\r\n\t\t}\r\n\t}\r\n}"], "mappings": ";;;;;;;;;;AAAA;;AACA;;AACA;;;;AAEA;AACA,IAAMA,IAAI,GAAG,MAAM,EAAN,GAAW,EAAX,GAAgB,EAAhB,GAAqB,IAAlC;AACO,IAAMC,QAAQ,GAAG,OAAOD,IAAxB;AAEP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;AACe,SAASE,mBAAT,CAA6BC,IAA7B,EAAmCC,IAAnC,QAAqF;EAAA,IAA1CC,QAA0C,QAA1CA,QAA0C;EAAA,IAAhCC,QAAgC,QAAhCA,QAAgC;EAAA,IAAtBC,GAAsB,QAAtBA,GAAsB;EAAA,IAAjBC,MAAiB,QAAjBA,MAAiB;EAAA,IAATC,KAAS,QAATA,KAAS;EACnG,IAAMC,SAAS,GAAGP,IAAI,CAACQ,OAAL,GAAeR,IAAI,CAACQ,OAAL,EAAf,GAAgCR,IAAlD;;EAEA,IAAMS,0BAA0B,GAAG,SAA7BA,0BAA6B,CAACC,IAAD;IAAA,OAAU,IAAAC,uCAAA,EAA4BD,IAA5B,EAAkCH,SAAlC,EAA6C;MAAEH,GAAG,EAAHA,GAAF;MAAOE,KAAK,EAALA;IAAP,CAA7C,CAAV;EAAA,CAAnC,CAHmG,CAKnG;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACA,IAAMM,gBAAgB,GAAGC,mBAAmB,CAACR,MAAM,GAAGJ,IAAH,GAAUE,QAAjB,EAA2BI,SAA3B,EAAsC;IACjFF,MAAM,EAANA,MADiF;IAEjFD,GAAG,EAAHA,GAFiF;IAGjFE,KAAK,EAALA,KAHiF;IAIjFJ,QAAQ,EAAEG,MAAM,GAAGH,QAAH,GAAcD,IAJmD,CAKjF;;EALiF,CAAtC,CAA5C;;EAQA,IAAIW,gBAAgB,KAAKE,SAAzB,EAAoC;IACnC;IACA;IACA;EACA;;EAED,IAAIC,gBAAJ;;EAEA,IAAId,IAAJ,EAAU;IACT,IAAIA,IAAI,CAACF,mBAAT,EAA8B;MAC7BgB,gBAAgB,GAAGd,IAAI,CAACF,mBAAL,CAAyBQ,SAAzB,EAAoC;QACtDE,0BAA0B,EAA1BA,0BADsD;QAEtDO,gBAAgB,EAAhBA,uBAFsD;QAGtDZ,GAAG,EAAHA,GAHsD;QAItDC,MAAM,EAANA,MAJsD;QAKtDC,KAAK,EAALA;MALsD,CAApC,CAAnB;IAOA;;IAED,IAAIS,gBAAgB,KAAKD,SAAzB,EAAoC;MACnC;MACA,IAAMJ,IAAI,GAAGT,IAAI,CAACS,IAAL,IAAaT,IAAI,CAACgB,QAA/B;;MACA,IAAIP,IAAJ,EAAU;QACT;QACA;QACA;QACA;QACAK,gBAAgB,GAAGN,0BAA0B,CAACC,IAAD,CAA7C;MACA;IACD;EACD;;EAED,IAAIK,gBAAgB,KAAKD,SAAzB,EAAoC;IACnC,OAAOF,gBAAP;EACA;;EAED,OAAOM,IAAI,CAACC,GAAL,CAASJ,gBAAT,EAA2BH,gBAA3B,CAAP;AACA;;AAEM,SAASQ,gBAAT,CAA0BC,iBAA1B,EAA6Cd,SAA7C,SAA0F;EAAA,IAAhCH,GAAgC,SAAhCA,GAAgC;EAAA,IAA3BC,MAA2B,SAA3BA,MAA2B;EAAA,IAAnBC,KAAmB,SAAnBA,KAAmB;EAAA,IAAZJ,QAAY,SAAZA,QAAY;EAChG;EACA;EACA;EACA;EACA;EACA,IAAMoB,OAAO,GAAG,IAAAC,0BAAA,EAAeF,iBAAf,EAAkC;IAAEd,SAAS,EAATA,SAAF;IAAaH,GAAG,EAAHA,GAAb;IAAkBC,MAAM,EAANA,MAAlB;IAA0BC,KAAK,EAALA,KAA1B;IAAiCJ,QAAQ,EAARA;EAAjC,CAAlC,CAAhB;;EACA,IAAIoB,OAAO,KAAKR,SAAhB,EAA2B;IAC1B;EACA;;EACD,IAAIT,MAAJ,EAAY;IACX;IACA;IACA;IACA;IACA;IACA,OAAOE,SAAS,GAAGe,OAAO,GAAG,IAAtB,GAA6B,CAApC;EACA,CAPD,MAOO;IACN;IACA;IACA;IACA;IACA;IAEA;IACA,IAAIA,OAAO,KAAK,CAAZ,IAAiBf,SAAS,KAAKH,GAAnC,EAAwC;MACvC,OAAON,QAAP;IACA;;IAED,OAAOS,SAAS,GAAGe,OAAO,GAAG,IAA7B;EACA;AACD;;AAEM,SAAST,mBAAT,CAA6BZ,IAA7B,EAAmCM,SAAnC,SAKJ;EAAA,IAJFH,GAIE,SAJFA,GAIE;EAAA,IAHFC,MAGE,SAHFA,MAGE;EAAA,IAFFC,KAEE,SAFFA,KAEE;EAAA,IADFJ,QACE,SADFA,QACE;;EACF,IAAID,IAAJ,EAAU;IACT,IAAMuB,aAAa,GAAGJ,gBAAgB,CAACnB,IAAD,EAAOM,SAAP,EAAkB;MACvDH,GAAG,EAAHA,GADuD;MAEvDC,MAAM,EAANA,MAFuD;MAGvDC,KAAK,EAALA,KAHuD;MAIvDJ,QAAQ,EAARA;IAJuD,CAAlB,CAAtC;;IAMA,IAAIsB,aAAa,KAAKV,SAAtB,EAAiC;MAChC;IACA;;IACD,OAAOU,aAAa,GAAGpB,GAAvB;EACA,CAXD,MAWO;IACN,IAAIC,MAAJ,EAAY;MACX;MACA;MACA,OAAOE,SAAS,GAAGH,GAAZ,GAAkB,CAAzB;IACA,CAJD,MAIO;MACN;MACA,OAAON,QAAP;IACA;EACD;AACD"}