"use strict";

var _getStep = _interopRequireDefault(require("./getStep.js"));

var _round = _interopRequireDefault(require("./round.js"));

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { "default": obj }; }

describe('steps/round', function () {
  it('should get step correctly (round: "floor")', function () {
    var getStepFor = function getStepFor(secondsPassed) {
      return (0, _getStep["default"])(_round["default"], secondsPassed, {
        round: 'floor',
        units: ['now', 'second', 'minute', 'hour', 'day', 'week', 'month', 'year']
      });
    };

    expect(getStepFor(0).formatAs).to.equal('now');
    expect(getStepFor(0.9).formatAs).to.equal('now');
    expect(getStepFor(1).formatAs).to.equal('second');
    expect(getStepFor(59.9).formatAs).to.equal('second');
    expect(getStepFor(60).formatAs).to.equal('minute');
    expect(getStepFor(60 * 60 - 1).formatAs).to.equal('minute');
    expect(getStepFor(60 * 60).formatAs).to.equal('hour');
    expect(getStepFor(24 * 60 * 60).formatAs).to.equal('day');
    expect(getStepFor(7 * 24 * 60 * 60).formatAs).to.equal('week');
  });
  it('should get step correctly (round: "round")', function () {
    var getStepFor = function getStepFor(secondsPassed) {
      return (0, _getStep["default"])(_round["default"], secondsPassed, {
        round: 'round',
        units: ['now', 'second', 'minute', 'hour', 'day', 'week', 'month', 'year']
      });
    };

    expect(getStepFor(0).formatAs).to.equal('now');
    expect(getStepFor(0.49).formatAs).to.equal('now');
    expect(getStepFor(0.5).formatAs).to.equal('second');
    expect(getStepFor(1).formatAs).to.equal('second');
    expect(getStepFor(59.4).formatAs).to.equal('second');
    expect(getStepFor(60).formatAs).to.equal('minute');
    expect(getStepFor(59.4 * 60).formatAs).to.equal('minute');
    expect(getStepFor(60 * 60).formatAs).to.equal('hour');
    expect(getStepFor(23.49 * 60 * 60).formatAs).to.equal('hour');
    expect(getStepFor(23.5 * 60 * 60).formatAs).to.equal('day');
    expect(getStepFor(7 * 24 * 60 * 60).formatAs).to.equal('week');
  });
  it('should use "day"s when "week"s are not allowed', function () {
    var getStepFor = function getStepFor(secondsPassed) {
      return (0, _getStep["default"])(_round["default"], secondsPassed, {
        units: ['now', 'second', 'minute', 'hour', 'day', 'month', 'year']
      });
    };

    expect(getStepFor(7 * 24 * 60 * 60).formatAs).to.equal('day');
  });
});
//# sourceMappingURL=round.test.js.map