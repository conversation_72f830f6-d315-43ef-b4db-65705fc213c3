{"version": 3, "file": "helpers.test.js", "names": ["describe", "it", "today", "Date", "getDate", "getTime", "should", "equal"], "sources": ["../../source/steps/helpers.test.js"], "sourcesContent": ["import { getDate } from './helpers.js'\r\n\r\ndescribe('steps/helpers', () => {\r\n\tit('should convert value to Date', () => {\r\n\t\tconst today = new Date()\r\n\t\tgetDate(today.getTime()).getTime().should.equal(today.getTime())\r\n\t\tgetDate(today).getTime().should.equal(today.getTime())\r\n\t})\r\n})"], "mappings": ";;AAAA;;AAEAA,QAAQ,CAAC,eAAD,EAAkB,YAAM;EAC/BC,EAAE,CAAC,8BAAD,EAAiC,YAAM;IACxC,IAAMC,KAAK,GAAG,IAAIC,IAAJ,EAAd;IACA,IAAAC,gBAAA,EAAQF,KAAK,CAACG,OAAN,EAAR,EAAyBA,OAAzB,GAAmCC,MAAnC,CAA0CC,KAA1C,CAAgDL,KAAK,CAACG,OAAN,EAAhD;IACA,IAAAD,gBAAA,EAAQF,KAAR,EAAeG,OAAf,GAAyBC,MAAzB,CAAgCC,KAAhC,CAAsCL,KAAK,CAACG,OAAN,EAAtC;EACA,CAJC,CAAF;AAKA,CANO,CAAR"}