{"version": 3, "file": "units.js", "names": ["minute", "hour", "day", "week", "month", "year", "getSecondsInUnit", "unit"], "sources": ["../../source/steps/units.js"], "sourcesContent": ["export const minute = 60 // in seconds\r\n\r\nexport const hour = 60 * minute // in seconds\r\n\r\nexport const day = 24 * hour // in seconds\r\n\r\nexport const week = 7 * day // in seconds\r\n\r\n// https://www.quora.com/What-is-the-average-number-of-days-in-a-month\r\nexport const month = 30.44 * day // in seconds\r\n\r\n// \"400 years have 146097 days (taking into account leap year rules)\"\r\nexport const year = (146097 / 400) * day // in seconds\r\n\r\nexport function getSecondsInUnit(unit) {\r\n\tswitch (unit) {\r\n\t\tcase 'second':\r\n\t\t\treturn 1\r\n\t\tcase 'minute':\r\n\t\t\treturn minute\r\n\t\tcase 'hour':\r\n\t\t\treturn hour\r\n\t\tcase 'day':\r\n\t\t\treturn day\r\n\t\tcase 'week':\r\n\t\t\treturn week\r\n\t\tcase 'month':\r\n\t\t\treturn month\r\n\t\tcase 'year':\r\n\t\t\treturn year\r\n\t}\r\n}\r\n\r\n// export function getPreviousUnitFor(unit) {\r\n// \tswitch (unit) {\r\n// \t\tcase 'second':\r\n// \t\t\treturn 'now'\r\n// \t\tcase 'minute':\r\n// \t\t\treturn 'second'\r\n// \t\tcase 'hour':\r\n// \t\t\treturn 'minute'\r\n// \t\tcase 'day':\r\n// \t\t\treturn 'hour'\r\n// \t\tcase 'week':\r\n// \t\t\treturn 'day'\r\n// \t\tcase 'month':\r\n// \t\t\treturn 'week'\r\n// \t\tcase 'year':\r\n// \t\t\treturn 'month'\r\n// \t}\r\n// }"], "mappings": ";;;;;;;;AAAO,IAAMA,MAAM,GAAG,EAAf,C,CAAkB;;;AAElB,IAAMC,IAAI,GAAG,KAAKD,MAAlB,C,CAAyB;;;AAEzB,IAAME,GAAG,GAAG,KAAKD,IAAjB,C,CAAsB;;;AAEtB,IAAME,IAAI,GAAG,IAAID,GAAjB,C,CAAqB;AAE5B;;;AACO,IAAME,KAAK,GAAG,QAAQF,GAAtB,C,CAA0B;AAEjC;;;AACO,IAAMG,IAAI,GAAI,SAAS,GAAV,GAAiBH,GAA9B,C,CAAkC;;;;AAElC,SAASI,gBAAT,CAA0BC,IAA1B,EAAgC;EACtC,QAAQA,IAAR;IACC,KAAK,QAAL;MACC,OAAO,CAAP;;IACD,KAAK,QAAL;MACC,OAAOP,MAAP;;IACD,KAAK,MAAL;MACC,OAAOC,IAAP;;IACD,KAAK,KAAL;MACC,OAAOC,GAAP;;IACD,KAAK,MAAL;MACC,OAAOC,IAAP;;IACD,KAAK,OAAL;MACC,OAAOC,KAAP;;IACD,KAAK,MAAL;MACC,OAAOC,IAAP;EAdF;AAgBA,C,CAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}