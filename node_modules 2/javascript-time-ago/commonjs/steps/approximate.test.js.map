{"version": 3, "file": "approximate.test.js", "names": ["describe", "it", "getStepFor", "secondsPassed", "getStep", "steps", "now", "units", "expect", "unit", "to", "equal", "factor", "granularity", "be", "undefined"], "sources": ["../../source/steps/approximate.test.js"], "sourcesContent": ["import getStep from './getStep.js'\r\nimport steps from './approximate.js'\r\n\r\ndescribe('steps/approximate', () => {\r\n\tit('should get step correctly', () => {\r\n\t\tconst getStepFor = (secondsPassed) => getStep(steps, secondsPassed, {\r\n\t\t\tnow: 0,\r\n\t\t\tunits: [\r\n\t\t\t\t'now',\r\n\t\t\t\t'second',\r\n\t\t\t\t'minute',\r\n\t\t\t\t'hour',\r\n\t\t\t\t'day',\r\n\t\t\t\t'week',\r\n\t\t\t\t'month',\r\n\t\t\t\t'year'\r\n\t\t\t]\r\n\t\t})\r\n\r\n\t\texpect(getStepFor(0).unit).to.equal('now')\r\n\t\texpect(getStepFor(1).unit).to.equal('now')\r\n\t\texpect(getStepFor(45).unit).to.equal('now')\r\n\r\n\t\texpect(getStepFor(46).unit).to.equal('minute')\r\n\t\texpect(getStepFor(46).factor).to.equal(60)\r\n\t\texpect(getStepFor(46).granularity).to.be.undefined\r\n\r\n\t\texpect(getStepFor(2.5 * 60 - 1).unit).to.equal('minute')\r\n\t\texpect(getStepFor(2.5 * 60 - 1).factor).to.equal(60)\r\n\t\texpect(getStepFor(2.5 * 60 - 1).granularity).to.be.undefined\r\n\r\n\t\texpect(getStepFor(2.5 * 60).unit).to.equal('minute')\r\n\t\texpect(getStepFor(2.5 * 60).factor).to.equal(60)\r\n\t\texpect(getStepFor(2.5 * 60).granularity).to.equal(5)\r\n\r\n\t\texpect(getStepFor(52.5 * 60 - 1).unit).to.equal('minute')\r\n\t\texpect(getStepFor(52.5 * 60 - 1).factor).to.equal(60)\r\n\t\texpect(getStepFor(52.5 * 60 - 1).granularity).to.equal(5)\r\n\r\n\t\texpect(getStepFor(52.5 * 60).unit).to.equal('hour')\r\n\t\texpect(getStepFor(52.5 * 60).factor).to.equal(60 * 60)\r\n\t})\r\n\r\n\tit('should get step correctly (\"now\" unit not allowed)', () => {\r\n\t\tconst getStepFor = (secondsPassed) => getStep(steps, secondsPassed, {\r\n\t\t\tnow: 0,\r\n\t\t\tunits: [\r\n\t\t\t\t'second',\r\n\t\t\t\t'minute',\r\n\t\t\t\t'hour',\r\n\t\t\t\t'day',\r\n\t\t\t\t'week',\r\n\t\t\t\t'month',\r\n\t\t\t\t'year'\r\n\t\t\t]\r\n\t\t})\r\n\r\n\t\texpect(getStepFor(0)).to.be.undefined\r\n\t\texpect(getStepFor(1).unit).to.equal('second')\r\n\t\texpect(getStepFor(45).unit).to.equal('second')\r\n\t\texpect(getStepFor(46).unit).to.equal('minute')\r\n\t})\r\n})"], "mappings": ";;AAAA;;AACA;;;;AAEAA,QAAQ,CAAC,mBAAD,EAAsB,YAAM;EACnCC,EAAE,CAAC,2BAAD,EAA8B,YAAM;IACrC,IAAMC,UAAU,GAAG,SAAbA,UAAa,CAACC,aAAD;MAAA,OAAmB,IAAAC,mBAAA,EAAQC,uBAAR,EAAeF,aAAf,EAA8B;QACnEG,GAAG,EAAE,CAD8D;QAEnEC,KAAK,EAAE,CACN,KADM,EAEN,QAFM,EAGN,QAHM,EAIN,MAJM,EAKN,KALM,EAMN,MANM,EAON,OAPM,EAQN,MARM;MAF4D,CAA9B,CAAnB;IAAA,CAAnB;;IAcAC,MAAM,CAACN,UAAU,CAAC,CAAD,CAAV,CAAcO,IAAf,CAAN,CAA2BC,EAA3B,CAA8BC,KAA9B,CAAoC,KAApC;IACAH,MAAM,CAACN,UAAU,CAAC,CAAD,CAAV,CAAcO,IAAf,CAAN,CAA2BC,EAA3B,CAA8BC,KAA9B,CAAoC,KAApC;IACAH,MAAM,CAACN,UAAU,CAAC,EAAD,CAAV,CAAeO,IAAhB,CAAN,CAA4BC,EAA5B,CAA+BC,KAA/B,CAAqC,KAArC;IAEAH,MAAM,CAACN,UAAU,CAAC,EAAD,CAAV,CAAeO,IAAhB,CAAN,CAA4BC,EAA5B,CAA+BC,KAA/B,CAAqC,QAArC;IACAH,MAAM,CAACN,UAAU,CAAC,EAAD,CAAV,CAAeU,MAAhB,CAAN,CAA8BF,EAA9B,CAAiCC,KAAjC,CAAuC,EAAvC;IACAH,MAAM,CAACN,UAAU,CAAC,EAAD,CAAV,CAAeW,WAAhB,CAAN,CAAmCH,EAAnC,CAAsCI,EAAtC,CAAyCC,SAAzC;IAEAP,MAAM,CAACN,UAAU,CAAC,MAAM,EAAN,GAAW,CAAZ,CAAV,CAAyBO,IAA1B,CAAN,CAAsCC,EAAtC,CAAyCC,KAAzC,CAA+C,QAA/C;IACAH,MAAM,CAACN,UAAU,CAAC,MAAM,EAAN,GAAW,CAAZ,CAAV,CAAyBU,MAA1B,CAAN,CAAwCF,EAAxC,CAA2CC,KAA3C,CAAiD,EAAjD;IACAH,MAAM,CAACN,UAAU,CAAC,MAAM,EAAN,GAAW,CAAZ,CAAV,CAAyBW,WAA1B,CAAN,CAA6CH,EAA7C,CAAgDI,EAAhD,CAAmDC,SAAnD;IAEAP,MAAM,CAACN,UAAU,CAAC,MAAM,EAAP,CAAV,CAAqBO,IAAtB,CAAN,CAAkCC,EAAlC,CAAqCC,KAArC,CAA2C,QAA3C;IACAH,MAAM,CAACN,UAAU,CAAC,MAAM,EAAP,CAAV,CAAqBU,MAAtB,CAAN,CAAoCF,EAApC,CAAuCC,KAAvC,CAA6C,EAA7C;IACAH,MAAM,CAACN,UAAU,CAAC,MAAM,EAAP,CAAV,CAAqBW,WAAtB,CAAN,CAAyCH,EAAzC,CAA4CC,KAA5C,CAAkD,CAAlD;IAEAH,MAAM,CAACN,UAAU,CAAC,OAAO,EAAP,GAAY,CAAb,CAAV,CAA0BO,IAA3B,CAAN,CAAuCC,EAAvC,CAA0CC,KAA1C,CAAgD,QAAhD;IACAH,MAAM,CAACN,UAAU,CAAC,OAAO,EAAP,GAAY,CAAb,CAAV,CAA0BU,MAA3B,CAAN,CAAyCF,EAAzC,CAA4CC,KAA5C,CAAkD,EAAlD;IACAH,MAAM,CAACN,UAAU,CAAC,OAAO,EAAP,GAAY,CAAb,CAAV,CAA0BW,WAA3B,CAAN,CAA8CH,EAA9C,CAAiDC,KAAjD,CAAuD,CAAvD;IAEAH,MAAM,CAACN,UAAU,CAAC,OAAO,EAAR,CAAV,CAAsBO,IAAvB,CAAN,CAAmCC,EAAnC,CAAsCC,KAAtC,CAA4C,MAA5C;IACAH,MAAM,CAACN,UAAU,CAAC,OAAO,EAAR,CAAV,CAAsBU,MAAvB,CAAN,CAAqCF,EAArC,CAAwCC,KAAxC,CAA8C,KAAK,EAAnD;EACA,CArCC,CAAF;EAuCAV,EAAE,CAAC,oDAAD,EAAuD,YAAM;IAC9D,IAAMC,UAAU,GAAG,SAAbA,UAAa,CAACC,aAAD;MAAA,OAAmB,IAAAC,mBAAA,EAAQC,uBAAR,EAAeF,aAAf,EAA8B;QACnEG,GAAG,EAAE,CAD8D;QAEnEC,KAAK,EAAE,CACN,QADM,EAEN,QAFM,EAGN,MAHM,EAIN,KAJM,EAKN,MALM,EAMN,OANM,EAON,MAPM;MAF4D,CAA9B,CAAnB;IAAA,CAAnB;;IAaAC,MAAM,CAACN,UAAU,CAAC,CAAD,CAAX,CAAN,CAAsBQ,EAAtB,CAAyBI,EAAzB,CAA4BC,SAA5B;IACAP,MAAM,CAACN,UAAU,CAAC,CAAD,CAAV,CAAcO,IAAf,CAAN,CAA2BC,EAA3B,CAA8BC,KAA9B,CAAoC,QAApC;IACAH,MAAM,CAACN,UAAU,CAAC,EAAD,CAAV,CAAeO,IAAhB,CAAN,CAA4BC,EAA5B,CAA+BC,KAA/B,CAAqC,QAArC;IACAH,MAAM,CAACN,UAAU,CAAC,EAAD,CAAV,CAAeO,IAAhB,CAAN,CAA4BC,EAA5B,CAA+BC,KAA/B,CAAqC,QAArC;EACA,CAlBC,CAAF;AAmBA,CA3DO,CAAR"}