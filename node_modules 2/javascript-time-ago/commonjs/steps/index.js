"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
Object.defineProperty(exports, "approximate", {
  enumerable: true,
  get: function get() {
    return _approximate["default"];
  }
});
Object.defineProperty(exports, "canonical", {
  enumerable: true,
  get: function get() {
    return _round["default"];
  }
});
Object.defineProperty(exports, "convenient", {
  enumerable: true,
  get: function get() {
    return _approximate["default"];
  }
});
Object.defineProperty(exports, "day", {
  enumerable: true,
  get: function get() {
    return _units.day;
  }
});
Object.defineProperty(exports, "getDate", {
  enumerable: true,
  get: function get() {
    return _helpers.getDate;
  }
});
Object.defineProperty(exports, "hour", {
  enumerable: true,
  get: function get() {
    return _units.hour;
  }
});
Object.defineProperty(exports, "minute", {
  enumerable: true,
  get: function get() {
    return _units.minute;
  }
});
Object.defineProperty(exports, "month", {
  enumerable: true,
  get: function get() {
    return _units.month;
  }
});
Object.defineProperty(exports, "round", {
  enumerable: true,
  get: function get() {
    return _round["default"];
  }
});
Object.defineProperty(exports, "week", {
  enumerable: true,
  get: function get() {
    return _units.week;
  }
});
Object.defineProperty(exports, "year", {
  enumerable: true,
  get: function get() {
    return _units.year;
  }
});

var _approximate = _interopRequireDefault(require("./approximate.js"));

var _round = _interopRequireDefault(require("./round.js"));

var _units = require("./units.js");

var _helpers = require("./helpers.js");

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { "default": obj }; }
//# sourceMappingURL=index.js.map