{"version": 3, "file": "getStepDenominator.test.js", "names": ["describe", "it", "getStepDenominator", "unit", "should", "equal", "formatAs"], "sources": ["../../source/steps/getStepDenominator.test.js"], "sourcesContent": ["import getStepDenominator from './getStepDenominator.js'\r\n\r\ndescribe('getStepDenominator', () => {\r\n\tit('should support the older \"unit\" name', () => {\r\n\t\tgetStepDenominator({ unit: 'minute' }).should.equal(60)\r\n\t})\r\n\r\n\tit('should return 1 as a default \"denominator\"', () => {\r\n\t\tgetStepDenominator({ formatAs: 'exotic' }).should.equal(1)\r\n\t})\r\n})"], "mappings": ";;AAAA;;;;AAEAA,QAAQ,CAAC,oBAAD,EAAuB,YAAM;EACpCC,EAAE,CAAC,sCAAD,EAAyC,YAAM;IAChD,IAAAC,8BAAA,EAAmB;MAAEC,IAAI,EAAE;IAAR,CAAnB,EAAuCC,MAAvC,CAA8CC,KAA9C,CAAoD,EAApD;EACA,CAFC,CAAF;EAIAJ,EAAE,CAAC,4CAAD,EAA+C,YAAM;IACtD,IAAAC,8BAAA,EAAmB;MAAEI,QAAQ,EAAE;IAAZ,CAAnB,EAA2CF,MAA3C,CAAkDC,KAAlD,CAAwD,CAAxD;EACA,CAFC,CAAF;AAGA,CARO,CAAR"}