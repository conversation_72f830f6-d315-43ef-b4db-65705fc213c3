{"version": 3, "file": "miniMinute.test.js", "names": ["describe", "it", "timeAgo", "TimeAgo", "formatInterval", "secondsPassed", "format", "now", "style", "round", "should", "equal", "minute", "hour", "day", "month"], "sources": ["../../source/style/miniMinute.test.js"], "sourcesContent": ["import style from './miniMinute.js'\r\nimport TimeAgo from '../TimeAgo.js'\r\nimport { hour, minute, day, month, year } from '../steps/index.js'\r\n\r\ndescribe('style/mini-now', () => {\r\n\tit('should format relative date/time (round: \"floor\")', () => {\r\n\t\tconst timeAgo = new TimeAgo('en')\r\n\t\tconst formatInterval = (secondsPassed) => timeAgo.format(-secondsPassed * 1000, { now: 0, ...style, round: 'floor' })\r\n\r\n\t\tformatInterval(0).should.equal('0m')\r\n\t\tformatInterval(0.9).should.equal('0m')\r\n\t\tformatInterval(1).should.equal('0m')\r\n\t\tformatInterval(59.9).should.equal('0m')\r\n\t\tformatInterval(60).should.equal('1m')\r\n\t\tformatInterval(1.9 * minute).should.equal('1m')\r\n\t\tformatInterval(2 * minute).should.equal('2m')\r\n\t\tformatInterval(2.9 * minute).should.equal('2m')\r\n\t\tformatInterval(3 * minute).should.equal('3m')\r\n\t\t// …\r\n\t\tformatInterval(59.9 * minute).should.equal('59m')\r\n\t\tformatInterval(60 * minute).should.equal('1h')\r\n\t\tformatInterval(1.9 * hour).should.equal('1h')\r\n\t\tformatInterval(2 * hour).should.equal('2h')\r\n\t\tformatInterval(2.9 * hour).should.equal('2h')\r\n\t\tformatInterval(3 * hour).should.equal('3h')\r\n\t\t// …\r\n\t\tformatInterval(23.9 * hour).should.equal('23h')\r\n\t\tformatInterval(24 * hour).should.equal('1d')\r\n\t\tformatInterval(2 * day).should.equal('2d')\r\n\t\tformatInterval(7 * day).should.equal('7d')\r\n\t\tformatInterval(30 * day).should.equal('30d')\r\n\t\tformatInterval(month).should.equal('1mo')\r\n\t\tformatInterval(360 * day).should.equal('11mo')\r\n\t\tformatInterval(366 * day).should.equal('1yr')\r\n\t})\r\n})"], "mappings": ";;AAAA;;AACA;;AACA;;;;;;;;;;AAEAA,QAAQ,CAAC,gBAAD,EAAmB,YAAM;EAChCC,EAAE,CAAC,mDAAD,EAAsD,YAAM;IAC7D,IAAMC,OAAO,GAAG,IAAIC,mBAAJ,CAAY,IAAZ,CAAhB;;IACA,IAAMC,cAAc,GAAG,SAAjBA,cAAiB,CAACC,aAAD;MAAA,OAAmBH,OAAO,CAACI,MAAR,CAAe,CAACD,aAAD,GAAiB,IAAhC;QAAwCE,GAAG,EAAE;MAA7C,GAAmDC,sBAAnD;QAA0DC,KAAK,EAAE;MAAjE,GAAnB;IAAA,CAAvB;;IAEAL,cAAc,CAAC,CAAD,CAAd,CAAkBM,MAAlB,CAAyBC,KAAzB,CAA+B,IAA/B;IACAP,cAAc,CAAC,GAAD,CAAd,CAAoBM,MAApB,CAA2BC,KAA3B,CAAiC,IAAjC;IACAP,cAAc,CAAC,CAAD,CAAd,CAAkBM,MAAlB,CAAyBC,KAAzB,CAA+B,IAA/B;IACAP,cAAc,CAAC,IAAD,CAAd,CAAqBM,MAArB,CAA4BC,KAA5B,CAAkC,IAAlC;IACAP,cAAc,CAAC,EAAD,CAAd,CAAmBM,MAAnB,CAA0BC,KAA1B,CAAgC,IAAhC;IACAP,cAAc,CAAC,MAAMQ,aAAP,CAAd,CAA6BF,MAA7B,CAAoCC,KAApC,CAA0C,IAA1C;IACAP,cAAc,CAAC,IAAIQ,aAAL,CAAd,CAA2BF,MAA3B,CAAkCC,KAAlC,CAAwC,IAAxC;IACAP,cAAc,CAAC,MAAMQ,aAAP,CAAd,CAA6BF,MAA7B,CAAoCC,KAApC,CAA0C,IAA1C;IACAP,cAAc,CAAC,IAAIQ,aAAL,CAAd,CAA2BF,MAA3B,CAAkCC,KAAlC,CAAwC,IAAxC,EAZ6D,CAa7D;;IACAP,cAAc,CAAC,OAAOQ,aAAR,CAAd,CAA8BF,MAA9B,CAAqCC,KAArC,CAA2C,KAA3C;IACAP,cAAc,CAAC,KAAKQ,aAAN,CAAd,CAA4BF,MAA5B,CAAmCC,KAAnC,CAAyC,IAAzC;IACAP,cAAc,CAAC,MAAMS,WAAP,CAAd,CAA2BH,MAA3B,CAAkCC,KAAlC,CAAwC,IAAxC;IACAP,cAAc,CAAC,IAAIS,WAAL,CAAd,CAAyBH,MAAzB,CAAgCC,KAAhC,CAAsC,IAAtC;IACAP,cAAc,CAAC,MAAMS,WAAP,CAAd,CAA2BH,MAA3B,CAAkCC,KAAlC,CAAwC,IAAxC;IACAP,cAAc,CAAC,IAAIS,WAAL,CAAd,CAAyBH,MAAzB,CAAgCC,KAAhC,CAAsC,IAAtC,EAnB6D,CAoB7D;;IACAP,cAAc,CAAC,OAAOS,WAAR,CAAd,CAA4BH,MAA5B,CAAmCC,KAAnC,CAAyC,KAAzC;IACAP,cAAc,CAAC,KAAKS,WAAN,CAAd,CAA0BH,MAA1B,CAAiCC,KAAjC,CAAuC,IAAvC;IACAP,cAAc,CAAC,IAAIU,UAAL,CAAd,CAAwBJ,MAAxB,CAA+BC,KAA/B,CAAqC,IAArC;IACAP,cAAc,CAAC,IAAIU,UAAL,CAAd,CAAwBJ,MAAxB,CAA+BC,KAA/B,CAAqC,IAArC;IACAP,cAAc,CAAC,KAAKU,UAAN,CAAd,CAAyBJ,MAAzB,CAAgCC,KAAhC,CAAsC,KAAtC;IACAP,cAAc,CAACW,YAAD,CAAd,CAAsBL,MAAtB,CAA6BC,KAA7B,CAAmC,KAAnC;IACAP,cAAc,CAAC,MAAMU,UAAP,CAAd,CAA0BJ,MAA1B,CAAiCC,KAAjC,CAAuC,MAAvC;IACAP,cAAc,CAAC,MAAMU,UAAP,CAAd,CAA0BJ,MAA1B,CAAiCC,KAAjC,CAAuC,KAAvC;EACA,CA7BC,CAAF;AA8BA,CA/BO,CAAR"}