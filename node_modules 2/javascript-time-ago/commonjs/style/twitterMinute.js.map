{"version": 3, "file": "twitterMinute.js", "names": ["twitter", "steps", "filter", "step", "formatAs"], "sources": ["../../source/style/twitterMinute.js"], "sourcesContent": ["import twitter from './twitter.js'\r\n\r\nexport default {\r\n\t...twitter,\r\n\t// Skip \"seconds\".\r\n\tsteps: twitter.steps.filter(step => step.formatAs !== 'second')\r\n}"], "mappings": ";;;;;;;AAAA;;;;;;;;;;+<PERSON><PERSON><PERSON>,mB;EACH;EACAC,KAAK,EAAED,mBAAA,CAAQC,KAAR,CAAcC,MAAd,CAAqB,UAAAC,IAAI;IAAA,OAAIA,IAAI,CAACC,QAAL,KAAkB,QAAtB;EAAA,CAAzB"}