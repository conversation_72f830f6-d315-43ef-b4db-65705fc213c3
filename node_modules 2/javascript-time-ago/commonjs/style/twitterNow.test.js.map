{"version": 3, "file": "twitterNow.test.js", "names": ["describe", "it", "timeAgo", "TimeAgo", "now", "Date", "getTime", "formatInterval", "secondsPassed", "format", "twitter", "round", "should", "equal", "minute", "hour", "day", "month", "year"], "sources": ["../../source/style/twitterNow.test.js"], "sourcesContent": ["import twitter from './twitterNow.js'\r\nimport TimeAgo from '../TimeAgo.js'\r\nimport { hour, minute, day, month, year } from '../steps/index.js'\r\n\r\ndescribe('style/twitterNow', () => {\r\n\tit('should format Twitter style relative time (English) (round: \"floor\")', () => {\r\n\t\tconst timeAgo = new TimeAgo('en')\r\n\r\n\t\t// April 10th, 2016.\r\n\t\tconst now = new Date(2016, 3, 10, 22, 59).getTime()\r\n\t\tconst formatInterval = (secondsPassed) => timeAgo.format(now - secondsPassed * 1000, { now, ...twitter, round: 'floor' })\r\n\r\n\t\tformatInterval(0).should.equal('now')\r\n\t\tformatInterval(0.9).should.equal('now')\r\n\t\tformatInterval(1).should.equal('1s')\r\n\t\tformatInterval(59.9).should.equal('59s')\r\n\t\tformatInterval(60).should.equal('1m')\r\n\t\tformatInterval(1.9 * minute).should.equal('1m')\r\n\t\tformatInterval(2 * minute).should.equal('2m')\r\n\t\tformatInterval(2.9 * minute).should.equal('2m')\r\n\t\tformatInterval(3 * minute).should.equal('3m')\r\n\t\t// …\r\n\t\tformatInterval(59.9 * minute).should.equal('59m')\r\n\t\tformatInterval(60 * minute).should.equal('1h')\r\n\t\tformatInterval(1.9 * hour).should.equal('1h')\r\n\t\tformatInterval(2 * hour).should.equal('2h')\r\n\t\tformatInterval(2.9 * hour).should.equal('2h')\r\n\t\tformatInterval(3 * hour).should.equal('3h')\r\n\t\t// …\r\n\t\tformatInterval(23.9 * hour).should.equal('23h')\r\n\t\tformatInterval(day + 2 * minute + hour).should.equal('Apr 9')\r\n\t\t// …\r\n\t\t// `month` is about 30.5 days.\r\n\t\tformatInterval(month * 3).should.equal('Jan 10')\r\n\t\tformatInterval(month * 4).should.equal('Dec 11, 2015')\r\n\t\tformatInterval(year).should.equal('Apr 11, 2015')\r\n\r\n\t\t// Test future dates.\r\n\t\t// `month` is about 30.5 days.\r\n\t\tformatInterval(-1 * month * 8).should.equal('Dec 10')\r\n\t\tformatInterval(-1 * month * 9).should.equal('Jan 9, 2017')\r\n\t})\r\n\r\n\tit('should format Twitter style relative time (Russian) (round: \"floor\")', () => {\r\n\t\tconst timeAgo = new TimeAgo('ru')\r\n\r\n\t\tconst now = new Date(2016, 3, 10, 22, 59).getTime()\r\n\t\tconst formatInterval = (secondsPassed) => timeAgo.format(now - secondsPassed * 1000, { now, ...twitter, round: 'floor' })\r\n\r\n\t\tformatInterval(0).should.equal('сейчас')\r\n\t\tformatInterval(0.9).should.equal('сейчас')\r\n\t\tformatInterval(1).should.equal('1 с')\r\n\t\tformatInterval(60).should.equal('1 мин')\r\n\t\tformatInterval(60 * minute).should.equal('1 ч')\r\n\t\tformatInterval(day + 62 * minute).should.equal('9 апр.')\r\n\t\tformatInterval(year).should.equal('11 апр. 2015 г.')\r\n\t})\r\n})"], "mappings": ";;AAAA;;AACA;;AACA;;;;;;;;;;AAEAA,QAAQ,CAAC,kBAAD,EAAqB,YAAM;EAClCC,EAAE,CAAC,sEAAD,EAAyE,YAAM;IAChF,IAAMC,OAAO,GAAG,IAAIC,mBAAJ,CAAY,IAAZ,CAAhB,CADgF,CAGhF;;IACA,IAAMC,GAAG,GAAG,IAAIC,IAAJ,CAAS,IAAT,EAAe,CAAf,EAAkB,EAAlB,EAAsB,EAAtB,EAA0B,EAA1B,EAA8BC,OAA9B,EAAZ;;IACA,IAAMC,cAAc,GAAG,SAAjBA,cAAiB,CAACC,aAAD;MAAA,OAAmBN,OAAO,CAACO,MAAR,CAAeL,GAAG,GAAGI,aAAa,GAAG,IAArC;QAA6CJ,GAAG,EAAHA;MAA7C,GAAqDM,sBAArD;QAA8DC,KAAK,EAAE;MAArE,GAAnB;IAAA,CAAvB;;IAEAJ,cAAc,CAAC,CAAD,CAAd,CAAkBK,MAAlB,CAAyBC,KAAzB,CAA+B,KAA/B;IACAN,cAAc,CAAC,GAAD,CAAd,CAAoBK,MAApB,CAA2BC,KAA3B,CAAiC,KAAjC;IACAN,cAAc,CAAC,CAAD,CAAd,CAAkBK,MAAlB,CAAyBC,KAAzB,CAA+B,IAA/B;IACAN,cAAc,CAAC,IAAD,CAAd,CAAqBK,MAArB,CAA4BC,KAA5B,CAAkC,KAAlC;IACAN,cAAc,CAAC,EAAD,CAAd,CAAmBK,MAAnB,CAA0BC,KAA1B,CAAgC,IAAhC;IACAN,cAAc,CAAC,MAAMO,aAAP,CAAd,CAA6BF,MAA7B,CAAoCC,KAApC,CAA0C,IAA1C;IACAN,cAAc,CAAC,IAAIO,aAAL,CAAd,CAA2BF,MAA3B,CAAkCC,KAAlC,CAAwC,IAAxC;IACAN,cAAc,CAAC,MAAMO,aAAP,CAAd,CAA6BF,MAA7B,CAAoCC,KAApC,CAA0C,IAA1C;IACAN,cAAc,CAAC,IAAIO,aAAL,CAAd,CAA2BF,MAA3B,CAAkCC,KAAlC,CAAwC,IAAxC,EAfgF,CAgBhF;;IACAN,cAAc,CAAC,OAAOO,aAAR,CAAd,CAA8BF,MAA9B,CAAqCC,KAArC,CAA2C,KAA3C;IACAN,cAAc,CAAC,KAAKO,aAAN,CAAd,CAA4BF,MAA5B,CAAmCC,KAAnC,CAAyC,IAAzC;IACAN,cAAc,CAAC,MAAMQ,WAAP,CAAd,CAA2BH,MAA3B,CAAkCC,KAAlC,CAAwC,IAAxC;IACAN,cAAc,CAAC,IAAIQ,WAAL,CAAd,CAAyBH,MAAzB,CAAgCC,KAAhC,CAAsC,IAAtC;IACAN,cAAc,CAAC,MAAMQ,WAAP,CAAd,CAA2BH,MAA3B,CAAkCC,KAAlC,CAAwC,IAAxC;IACAN,cAAc,CAAC,IAAIQ,WAAL,CAAd,CAAyBH,MAAzB,CAAgCC,KAAhC,CAAsC,IAAtC,EAtBgF,CAuBhF;;IACAN,cAAc,CAAC,OAAOQ,WAAR,CAAd,CAA4BH,MAA5B,CAAmCC,KAAnC,CAAyC,KAAzC;IACAN,cAAc,CAACS,UAAA,GAAM,IAAIF,aAAV,GAAmBC,WAApB,CAAd,CAAwCH,MAAxC,CAA+CC,KAA/C,CAAqD,OAArD,EAzBgF,CA0BhF;IACA;;IACAN,cAAc,CAACU,YAAA,GAAQ,CAAT,CAAd,CAA0BL,MAA1B,CAAiCC,KAAjC,CAAuC,QAAvC;IACAN,cAAc,CAACU,YAAA,GAAQ,CAAT,CAAd,CAA0BL,MAA1B,CAAiCC,KAAjC,CAAuC,cAAvC;IACAN,cAAc,CAACW,WAAD,CAAd,CAAqBN,MAArB,CAA4BC,KAA5B,CAAkC,cAAlC,EA9BgF,CAgChF;IACA;;IACAN,cAAc,CAAC,CAAC,CAAD,GAAKU,YAAL,GAAa,CAAd,CAAd,CAA+BL,MAA/B,CAAsCC,KAAtC,CAA4C,QAA5C;IACAN,cAAc,CAAC,CAAC,CAAD,GAAKU,YAAL,GAAa,CAAd,CAAd,CAA+BL,MAA/B,CAAsCC,KAAtC,CAA4C,aAA5C;EACA,CApCC,CAAF;EAsCAZ,EAAE,CAAC,sEAAD,EAAyE,YAAM;IAChF,IAAMC,OAAO,GAAG,IAAIC,mBAAJ,CAAY,IAAZ,CAAhB;IAEA,IAAMC,GAAG,GAAG,IAAIC,IAAJ,CAAS,IAAT,EAAe,CAAf,EAAkB,EAAlB,EAAsB,EAAtB,EAA0B,EAA1B,EAA8BC,OAA9B,EAAZ;;IACA,IAAMC,cAAc,GAAG,SAAjBA,cAAiB,CAACC,aAAD;MAAA,OAAmBN,OAAO,CAACO,MAAR,CAAeL,GAAG,GAAGI,aAAa,GAAG,IAArC;QAA6CJ,GAAG,EAAHA;MAA7C,GAAqDM,sBAArD;QAA8DC,KAAK,EAAE;MAArE,GAAnB;IAAA,CAAvB;;IAEAJ,cAAc,CAAC,CAAD,CAAd,CAAkBK,MAAlB,CAAyBC,KAAzB,CAA+B,QAA/B;IACAN,cAAc,CAAC,GAAD,CAAd,CAAoBK,MAApB,CAA2BC,KAA3B,CAAiC,QAAjC;IACAN,cAAc,CAAC,CAAD,CAAd,CAAkBK,MAAlB,CAAyBC,KAAzB,CAA+B,KAA/B;IACAN,cAAc,CAAC,EAAD,CAAd,CAAmBK,MAAnB,CAA0BC,KAA1B,CAAgC,OAAhC;IACAN,cAAc,CAAC,KAAKO,aAAN,CAAd,CAA4BF,MAA5B,CAAmCC,KAAnC,CAAyC,KAAzC;IACAN,cAAc,CAACS,UAAA,GAAM,KAAKF,aAAZ,CAAd,CAAkCF,MAAlC,CAAyCC,KAAzC,CAA+C,QAA/C;IACAN,cAAc,CAACW,WAAD,CAAd,CAAqBN,MAArB,CAA4BC,KAA5B,CAAkC,iBAAlC;EACA,CAbC,CAAF;AAcA,CArDO,CAAR"}