{"version": 3, "file": "miniMinuteNow.js", "names": ["miniMinute", "steps", "formatAs", "concat"], "sources": ["../../source/style/miniMinuteNow.js"], "sourcesContent": ["import miniMinute from './miniMinute.js'\r\n\r\nexport default {\r\n\t...miniMinute,\r\n\t// Add \"now\".\r\n\tsteps: [{ formatAs: 'now' }].concat(miniMinute.steps)\r\n}"], "mappings": ";;;;;;;AAAA;;;;;;;;;;+CAGI<PERSON>,sB;EACH;EACAC,KAAK,EAAE,CAAC;IAAEC,QAAQ,EAAE;EAAZ,CAAD,EAAsBC,MAAtB,CAA6BH,sBAAA,CAAWC,KAAxC"}