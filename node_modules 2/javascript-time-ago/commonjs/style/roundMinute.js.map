{"version": 3, "file": "roundMinute.js", "names": ["round", "steps", "filter", "step", "formatAs"], "sources": ["../../source/style/roundMinute.js"], "sourcesContent": ["import round from './round.js'\r\n\r\n// just now\r\n// 1 minute ago\r\n// 2 minutes ago\r\n// …\r\n// 59 minutes ago\r\n// 1 minute ago\r\n// 2 minutes ago\r\n// …\r\n// 59 minutes ago\r\n// 1 hour ago\r\n// 2 hours ago\r\n// …\r\n// 24 hours ago\r\n// 1 day ago\r\n// 2 days ago\r\n// …\r\n// 6 days ago\r\n// 1 week ago\r\n// 2 weeks ago\r\n// 3 weeks ago\r\n// 4 weeks ago\r\n// 1 month ago\r\n// 2 months ago\r\n// …\r\n// 11 months ago\r\n// 1 year ago\r\n// 2 years ago\r\n// …\r\n//\r\nexport default {\r\n\t...round,\r\n\t// Skip \"seconds\".\r\n\tsteps: round.steps.filter(step => step.formatAs !== 'second')\r\n}"], "mappings": ";;;;;;;AAAA;;;;;;;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;+CAEIA,iB;EACH;EACAC,KAAK,EAAED,iBAAA,CAAMC,KAAN,CAAYC,MAAZ,CAAmB,UAAAC,IAAI;IAAA,OAAIA,IAAI,CAACC,QAAL,KAAkB,QAAtB;EAAA,CAAvB"}