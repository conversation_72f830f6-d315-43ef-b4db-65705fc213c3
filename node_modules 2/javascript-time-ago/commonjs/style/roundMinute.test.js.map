{"version": 3, "file": "roundMinute.test.js", "names": ["describe", "it", "timeAgo", "TimeAgo", "now", "Date", "getTime", "formatInterval", "secondsPassed", "format", "roundMinute", "round", "should", "equal", "day", "month", "year"], "sources": ["../../source/style/roundMinute.test.js"], "sourcesContent": ["import roundMinute from './roundMinute.js'\r\nimport TimeAgo from '../TimeAgo.js'\r\nimport { day, month, year } from '../steps/index.js'\r\n\r\ndescribe('style/round-minute', () => {\r\n\tit('should format relative time (English) (round: \"floor\")', () => {\r\n\t\tconst timeAgo = new TimeAgo('en')\r\n\r\n\t\tconst now = new Date(2016, 3, 10, 22, 59).getTime()\r\n\t\tconst formatInterval = (secondsPassed) => timeAgo.format(now - secondsPassed * 1000, { now, ...roundMinute, round: 'floor' })\r\n\r\n\t\tformatInterval(0).should.equal('just now')\r\n\t\tformatInterval(0.9).should.equal('just now')\r\n\t\tformatInterval(1).should.equal('just now')\r\n\t\tformatInterval(59.9).should.equal('just now')\r\n\t\tformatInterval(60).should.equal('1 minute ago')\r\n\t\tformatInterval(1.9 * 60).should.equal('1 minute ago')\r\n\t\tformatInterval(2 * 60).should.equal('2 minutes ago')\r\n\t\tformatInterval(2.9 * 60).should.equal('2 minutes ago')\r\n\t\tformatInterval(3 * 60).should.equal('3 minutes ago')\r\n\t\t// …\r\n\t\tformatInterval(59.9 * 60).should.equal('59 minutes ago')\r\n\t\tformatInterval(60 * 60).should.equal('1 hour ago')\r\n\t\tformatInterval(1.9 * 60 * 60).should.equal('1 hour ago')\r\n\t\tformatInterval(2 * 60 * 60).should.equal('2 hours ago')\r\n\t\tformatInterval(2.9 * 60 * 60).should.equal('2 hours ago')\r\n\t\tformatInterval(3 * 60 * 60).should.equal('3 hours ago')\r\n\t\t// …\r\n\t\tformatInterval(23.9 * 60 * 60).should.equal('23 hours ago')\r\n\t\tformatInterval(24 * 60 * 60).should.equal('1 day ago')\r\n\t\tformatInterval(1.9 * day).should.equal('1 day ago')\r\n\t\tformatInterval(2 * day).should.equal('2 days ago')\r\n\t\tformatInterval(2.9 * day).should.equal('2 days ago')\r\n\t\tformatInterval(3 * day).should.equal('3 days ago')\r\n\t\t// …\r\n\t\tformatInterval(6.9 * day).should.equal('6 days ago')\r\n\t\tformatInterval(7 * day).should.equal('1 week ago')\r\n\t\t// …\r\n\t\tformatInterval(3.9 * 7 * day).should.equal('3 weeks ago')\r\n\t\tformatInterval(4 * 7 * day).should.equal('4 weeks ago')\r\n\t\tformatInterval(30.51 * day).should.equal('1 month ago')\r\n\t\tformatInterval(1.9 * month).should.equal('1 month ago')\r\n\t\tformatInterval(2 * month).should.equal('2 months ago')\r\n\t\tformatInterval(2.9 * month).should.equal('2 months ago')\r\n\t\tformatInterval(3 * month).should.equal('3 months ago')\r\n\t\t// …\r\n\t\tformatInterval(11.9 * month).should.equal('11 months ago')\r\n\t\tformatInterval(12 * month).should.equal('1 year ago')\r\n\t\tformatInterval(1.99 * year).should.equal('1 year ago')\r\n\t\tformatInterval(2 * year).should.equal('2 years ago')\r\n\t\t// …\r\n\r\n\t\t// Test future dates.\r\n\t\tformatInterval(-1 * 3 * 60).should.equal('in 3 minutes')\r\n\t\tformatInterval(-1 * month * 8).should.equal('in 8 months')\r\n\t})\r\n\r\n\tit('should format relative time (English)', () => {\r\n\t\tconst timeAgo = new TimeAgo('en')\r\n\r\n\t\tconst now = new Date(2016, 3, 10, 22, 59).getTime()\r\n\t\tconst formatInterval = (secondsPassed) => timeAgo.format(now - secondsPassed * 1000, { now, ...roundMinute })\r\n\r\n\t\tformatInterval(0).should.equal('just now')\r\n\t\tformatInterval(0.49).should.equal('just now')\r\n\t\tformatInterval(0.5).should.equal('just now')\r\n\t\tformatInterval(29.9).should.equal('just now')\r\n\t\tformatInterval(30).should.equal('1 minute ago')\r\n\t\tformatInterval(1.49 * 60).should.equal('1 minute ago')\r\n\t\tformatInterval(1.5 * 60).should.equal('2 minutes ago')\r\n\t\tformatInterval(2.49 * 60).should.equal('2 minutes ago')\r\n\t\tformatInterval(2.5 * 60).should.equal('3 minutes ago')\r\n\t})\r\n})"], "mappings": ";;AAAA;;AACA;;AACA;;;;;;;;;;AAEAA,QAAQ,CAAC,oBAAD,EAAuB,YAAM;EACpCC,EAAE,CAAC,wDAAD,EAA2D,YAAM;IAClE,IAAMC,OAAO,GAAG,IAAIC,mBAAJ,CAAY,IAAZ,CAAhB;IAEA,IAAMC,GAAG,GAAG,IAAIC,IAAJ,CAAS,IAAT,EAAe,CAAf,EAAkB,EAAlB,EAAsB,EAAtB,EAA0B,EAA1B,EAA8BC,OAA9B,EAAZ;;IACA,IAAMC,cAAc,GAAG,SAAjBA,cAAiB,CAACC,aAAD;MAAA,OAAmBN,OAAO,CAACO,MAAR,CAAeL,GAAG,GAAGI,aAAa,GAAG,IAArC;QAA6CJ,GAAG,EAAHA;MAA7C,GAAqDM,uBAArD;QAAkEC,KAAK,EAAE;MAAzE,GAAnB;IAAA,CAAvB;;IAEAJ,cAAc,CAAC,CAAD,CAAd,CAAkBK,MAAlB,CAAyBC,KAAzB,CAA+B,UAA/B;IACAN,cAAc,CAAC,GAAD,CAAd,CAAoBK,MAApB,CAA2BC,KAA3B,CAAiC,UAAjC;IACAN,cAAc,CAAC,CAAD,CAAd,CAAkBK,MAAlB,CAAyBC,KAAzB,CAA+B,UAA/B;IACAN,cAAc,CAAC,IAAD,CAAd,CAAqBK,MAArB,CAA4BC,KAA5B,CAAkC,UAAlC;IACAN,cAAc,CAAC,EAAD,CAAd,CAAmBK,MAAnB,CAA0BC,KAA1B,CAAgC,cAAhC;IACAN,cAAc,CAAC,MAAM,EAAP,CAAd,CAAyBK,MAAzB,CAAgCC,KAAhC,CAAsC,cAAtC;IACAN,cAAc,CAAC,IAAI,EAAL,CAAd,CAAuBK,MAAvB,CAA8BC,KAA9B,CAAoC,eAApC;IACAN,cAAc,CAAC,MAAM,EAAP,CAAd,CAAyBK,MAAzB,CAAgCC,KAAhC,CAAsC,eAAtC;IACAN,cAAc,CAAC,IAAI,EAAL,CAAd,CAAuBK,MAAvB,CAA8BC,KAA9B,CAAoC,eAApC,EAdkE,CAelE;;IACAN,cAAc,CAAC,OAAO,EAAR,CAAd,CAA0BK,MAA1B,CAAiCC,KAAjC,CAAuC,gBAAvC;IACAN,cAAc,CAAC,KAAK,EAAN,CAAd,CAAwBK,MAAxB,CAA+BC,KAA/B,CAAqC,YAArC;IACAN,cAAc,CAAC,MAAM,EAAN,GAAW,EAAZ,CAAd,CAA8BK,MAA9B,CAAqCC,KAArC,CAA2C,YAA3C;IACAN,cAAc,CAAC,IAAI,EAAJ,GAAS,EAAV,CAAd,CAA4BK,MAA5B,CAAmCC,KAAnC,CAAyC,aAAzC;IACAN,cAAc,CAAC,MAAM,EAAN,GAAW,EAAZ,CAAd,CAA8BK,MAA9B,CAAqCC,KAArC,CAA2C,aAA3C;IACAN,cAAc,CAAC,IAAI,EAAJ,GAAS,EAAV,CAAd,CAA4BK,MAA5B,CAAmCC,KAAnC,CAAyC,aAAzC,EArBkE,CAsBlE;;IACAN,cAAc,CAAC,OAAO,EAAP,GAAY,EAAb,CAAd,CAA+BK,MAA/B,CAAsCC,KAAtC,CAA4C,cAA5C;IACAN,cAAc,CAAC,KAAK,EAAL,GAAU,EAAX,CAAd,CAA6BK,MAA7B,CAAoCC,KAApC,CAA0C,WAA1C;IACAN,cAAc,CAAC,MAAMO,UAAP,CAAd,CAA0BF,MAA1B,CAAiCC,KAAjC,CAAuC,WAAvC;IACAN,cAAc,CAAC,IAAIO,UAAL,CAAd,CAAwBF,MAAxB,CAA+BC,KAA/B,CAAqC,YAArC;IACAN,cAAc,CAAC,MAAMO,UAAP,CAAd,CAA0BF,MAA1B,CAAiCC,KAAjC,CAAuC,YAAvC;IACAN,cAAc,CAAC,IAAIO,UAAL,CAAd,CAAwBF,MAAxB,CAA+BC,KAA/B,CAAqC,YAArC,EA5BkE,CA6BlE;;IACAN,cAAc,CAAC,MAAMO,UAAP,CAAd,CAA0BF,MAA1B,CAAiCC,KAAjC,CAAuC,YAAvC;IACAN,cAAc,CAAC,IAAIO,UAAL,CAAd,CAAwBF,MAAxB,CAA+BC,KAA/B,CAAqC,YAArC,EA/BkE,CAgClE;;IACAN,cAAc,CAAC,MAAM,CAAN,GAAUO,UAAX,CAAd,CAA8BF,MAA9B,CAAqCC,KAArC,CAA2C,aAA3C;IACAN,cAAc,CAAC,IAAI,CAAJ,GAAQO,UAAT,CAAd,CAA4BF,MAA5B,CAAmCC,KAAnC,CAAyC,aAAzC;IACAN,cAAc,CAAC,QAAQO,UAAT,CAAd,CAA4BF,MAA5B,CAAmCC,KAAnC,CAAyC,aAAzC;IACAN,cAAc,CAAC,MAAMQ,YAAP,CAAd,CAA4BH,MAA5B,CAAmCC,KAAnC,CAAyC,aAAzC;IACAN,cAAc,CAAC,IAAIQ,YAAL,CAAd,CAA0BH,MAA1B,CAAiCC,KAAjC,CAAuC,cAAvC;IACAN,cAAc,CAAC,MAAMQ,YAAP,CAAd,CAA4BH,MAA5B,CAAmCC,KAAnC,CAAyC,cAAzC;IACAN,cAAc,CAAC,IAAIQ,YAAL,CAAd,CAA0BH,MAA1B,CAAiCC,KAAjC,CAAuC,cAAvC,EAvCkE,CAwClE;;IACAN,cAAc,CAAC,OAAOQ,YAAR,CAAd,CAA6BH,MAA7B,CAAoCC,KAApC,CAA0C,eAA1C;IACAN,cAAc,CAAC,KAAKQ,YAAN,CAAd,CAA2BH,MAA3B,CAAkCC,KAAlC,CAAwC,YAAxC;IACAN,cAAc,CAAC,OAAOS,WAAR,CAAd,CAA4BJ,MAA5B,CAAmCC,KAAnC,CAAyC,YAAzC;IACAN,cAAc,CAAC,IAAIS,WAAL,CAAd,CAAyBJ,MAAzB,CAAgCC,KAAhC,CAAsC,aAAtC,EA5CkE,CA6ClE;IAEA;;IACAN,cAAc,CAAC,CAAC,CAAD,GAAK,CAAL,GAAS,EAAV,CAAd,CAA4BK,MAA5B,CAAmCC,KAAnC,CAAyC,cAAzC;IACAN,cAAc,CAAC,CAAC,CAAD,GAAKQ,YAAL,GAAa,CAAd,CAAd,CAA+BH,MAA/B,CAAsCC,KAAtC,CAA4C,aAA5C;EACA,CAlDC,CAAF;EAoDAZ,EAAE,CAAC,uCAAD,EAA0C,YAAM;IACjD,IAAMC,OAAO,GAAG,IAAIC,mBAAJ,CAAY,IAAZ,CAAhB;IAEA,IAAMC,GAAG,GAAG,IAAIC,IAAJ,CAAS,IAAT,EAAe,CAAf,EAAkB,EAAlB,EAAsB,EAAtB,EAA0B,EAA1B,EAA8BC,OAA9B,EAAZ;;IACA,IAAMC,cAAc,GAAG,SAAjBA,cAAiB,CAACC,aAAD;MAAA,OAAmBN,OAAO,CAACO,MAAR,CAAeL,GAAG,GAAGI,aAAa,GAAG,IAArC;QAA6CJ,GAAG,EAAHA;MAA7C,GAAqDM,uBAArD,EAAnB;IAAA,CAAvB;;IAEAH,cAAc,CAAC,CAAD,CAAd,CAAkBK,MAAlB,CAAyBC,KAAzB,CAA+B,UAA/B;IACAN,cAAc,CAAC,IAAD,CAAd,CAAqBK,MAArB,CAA4BC,KAA5B,CAAkC,UAAlC;IACAN,cAAc,CAAC,GAAD,CAAd,CAAoBK,MAApB,CAA2BC,KAA3B,CAAiC,UAAjC;IACAN,cAAc,CAAC,IAAD,CAAd,CAAqBK,MAArB,CAA4BC,KAA5B,CAAkC,UAAlC;IACAN,cAAc,CAAC,EAAD,CAAd,CAAmBK,MAAnB,CAA0BC,KAA1B,CAAgC,cAAhC;IACAN,cAAc,CAAC,OAAO,EAAR,CAAd,CAA0BK,MAA1B,CAAiCC,KAAjC,CAAuC,cAAvC;IACAN,cAAc,CAAC,MAAM,EAAP,CAAd,CAAyBK,MAAzB,CAAgCC,KAAhC,CAAsC,eAAtC;IACAN,cAAc,CAAC,OAAO,EAAR,CAAd,CAA0BK,MAA1B,CAAiCC,KAAjC,CAAuC,eAAvC;IACAN,cAAc,CAAC,MAAM,EAAP,CAAd,CAAyBK,MAAzB,CAAgCC,KAAhC,CAAsC,eAAtC;EACA,CAfC,CAAF;AAgBA,CArEO,CAAR"}